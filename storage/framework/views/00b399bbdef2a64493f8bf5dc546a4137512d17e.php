<?php $__env->startSection('content-header'); ?>

<audio style="display:none; height: 0" id="bg-music" preload="auto" src="/static/msg.mp3" loop="loop"></audio>
<script>
  function LA() {}
  LA.token = "<?php echo e(csrf_token(), false); ?>";
  var getting = {
    url:'/admin/sendNotice',
    dataType:'json',
    success: function (res) {
      console.log(res);
      if(res.count1 > 0){
　　　　　　　　　　
        toastr.options.onclick = function(){
          location.href='/admin/rechargeManual'; // 点击跳转页面
        };
　　　　toastr.options.timeOut=12000; // 保存2分钟
　　　　toastr.warning(res.msg1); // 提示文字
        var audio = document.getElementById('bg-music'); // 启用音频通知
        audio.src='/static/msg.mp3';
        audio.play();
        setTimeout(function(){
          audio.load(); // 1.5秒后关闭音频通知
        },2000);
      }
      if(res.count2 > 0){
　　　　　　　　　　
        toastr.options.onclick = function(){
          location.href='/admin/withdraw'; // 点击跳转页面
        };
　　　　toastr.options.timeOut=12000; // 保存2分钟
　　　　toastr.warning(res.msg2); // 提示文字
        var audio = document.getElementById('bg-music'); // 启用音频通知
        audio.src='/static/msg2.mp3';
        audio.play();
        setTimeout(function(){
          audio.load(); // 1.5秒后关闭音频通知
        },2000);
      }
      if(res.count3 > 0){
　　　　　　　　　　
        toastr.options.onclick = function(){
          location.href='/admin/user-auth'; // 点击跳转页面
        };
　　　　toastr.options.timeOut=12000; // 保存2分钟
　　　　toastr.warning(res.msg3); // 提示文字
        var audio = document.getElementById('bg-music'); // 启用音频通知
        audio.src='/static/msg3.mp3';
        audio.play();
        setTimeout(function(){
          audio.load(); // 1.5秒后关闭音频通知
        },2000);
      }
    },
    error: function (res) {
      console.log(res);
    }
  };
  //关键在这里，Ajax定时访问服务端，不断获取数据 ，这里是5秒请求一次。
  window.setInterval(function(){$.ajax(getting)},5000);
</script>
    <section class="content-header breadcrumbs-top">
        <?php if($header || $description): ?>
            <h1 class=" float-left">
                <span class="text-capitalize"><?php echo $header; ?></span>
                <small><?php echo $description; ?></small>
            </h1>
        <?php elseif($breadcrumb || config('admin.enable_default_breadcrumb')): ?>
            <div>&nbsp;</div>
        <?php endif; ?>

        <?php echo $__env->make('admin::partials.breadcrumb', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('admin::partials.alerts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('admin::partials.exception', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php echo $content; ?>


    <?php echo $__env->make('admin::partials.toastr', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('app'); ?>
    <?php echo Dcat\Admin\Admin::asset()->styleToHtml(); ?>


    <div class="content-header">
        <?php echo $__env->yieldContent('content-header'); ?>
    </div>

    <div class="content-body" id="app">
        
        <?php echo admin_section(AdminSection::APP_INNER_BEFORE); ?>


        <?php echo $__env->yieldContent('content'); ?>

        
        <?php echo admin_section(AdminSection::APP_INNER_AFTER); ?>

    </div>

    <?php echo Dcat\Admin\Admin::asset()->scriptToHtml(); ?>

    <?php echo Dcat\Admin\Admin::html(); ?>

<?php $__env->stopSection(); ?>

<?php if(! request()->pjax()): ?>
    <?php echo $__env->make('admin::layouts.page', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php else: ?>
    <title><?php echo e(Dcat\Admin\Admin::title(), false); ?> <?php if($header): ?> | <?php echo e($header, false); ?><?php endif; ?></title>

    <script>Dcat.pjaxResponded()</script>

    <?php echo Dcat\Admin\Admin::asset()->cssToHtml(); ?>

    <?php echo Dcat\Admin\Admin::asset()->jsToHtml(); ?>


    <?php echo $__env->yieldContent('app'); ?>
<?php endif; ?><?php /**PATH /www/wwwroot/htadmin.com/vendor/dcat/laravel-admin/src/../resources/views/layouts/content.blade.php ENDPATH**/ ?>