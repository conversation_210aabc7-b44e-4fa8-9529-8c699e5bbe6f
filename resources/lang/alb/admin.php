<?php

return [
    'scaffold' => [
        'header'            => 'Scaffold',
        'choose'            => 'choose',
        'table'             => 'Table',
        'model'             => 'Model',
        'controller'        => 'Controller',
        'repository'        => 'Repository',
        'add_field'         => 'Add field',
        'pk'                => 'Primary key',
        'soft_delete'       => 'Soft delete',
        'create_migration'  => 'Create migration',
        'create_model'      => 'Create model',
        'create_repository' => 'Create repository',
        'create_controller' => 'Create controller',
        'run_migrate'       => 'Run migrate',
        'create_lang'       => 'Create lang',
        'field'             => 'field',
        'translation'       => 'translation',
        'comment'           => 'comment',
        'default'           => 'default',
        'field_name'        => 'field name',
        'type'              => 'type',
        'nullable'          => 'nullable',
        'key'               => 'key',
    ],
    'client' => [
        'delete_confirm'    => 'Are you sure to delete this item ?',
        'confirm'           => 'Confirm',
        'cancel'            => 'Cancel',
        'refresh_succeeded' => 'Refresh succeeded !',
        'close'             => 'Close',
        'selected_options'  => ':num options selected',
        'exceed_max_item'   => 'Maximum items exceeded.',

        '500' => 'Internal server error !',
        '403' => 'Permission deny !',
        '401' => 'Unauthorized !',
        '419' => 'Page expired !',
    ],
    'online'                => 'Online',
    'login'                 => 'Login',
    'logout'                => 'Logout',
    'setting'               => 'Setting',
    'name'                  => 'Name',
    'username'              => 'Username',
    'user'                  => 'User',
    'alias'                 => 'Alias',
    'routes'                => 'Routes',
    'route_action'          => 'Route Action',
    'middleware'            => 'Middleware',
    'method'                => 'Method',
    'old_password'          => 'Old password',
    'password'              => 'Password',
    'password_confirmation' => 'Password confirmation',
    'old_password_error'    => 'Incorrect password',
    'remember_me'           => 'Remember me',
    'user_setting'          => 'User setting',
    'avatar'                => 'Avatar',
    'list'                  => 'List',
    'new'                   => 'New',
    'create'                => 'Create',
    'delete'                => 'Delete',
    'remove'                => 'Remove',
    'edit'                  => 'Edit',
    'quick_edit'            => 'Quick Edit',
    'view'                  => 'View',
    'continue_editing'      => 'Continue editing',
    'continue_creating'     => 'Continue creating',
    'detail'                => 'Detail',
    'browse'                => 'Browse',
    'reset'                 => 'Reset',
    'export'                => 'Export',
    'batch_delete'          => 'Batch delete',
    'save'                  => 'Save',
    'refresh'               => 'Refresh',
    'order'                 => 'Order',
    'expand'                => 'Expand',
    'collapse'              => 'Collapse',
    'filter'                => 'Filter',
    'search'                => 'Search',
    'close'                 => 'Close',
    'show'                  => 'Show',
    'entries'               => 'entries',
    'captcha'               => 'Captcha',
    'action'                => 'Action',
    'title'                 => 'Title',
    'description'           => 'Description',
    'back'                  => 'Back',
    'back_to_list'          => 'Back to List',
    'submit'                => 'Submit',
    'menu'                  => 'Menu',
    'input'                 => 'Input',
    'succeeded'             => 'Succeeded',
    'failed'                => 'Failed',
    'delete_confirm'        => 'Are you sure to delete this item ?',
    'delete_succeeded'      => 'Delete succeeded !',
    'delete_failed'         => 'Delete failed !',
    'update_succeeded'      => 'Update succeeded !',
    'update_failed'         => 'Update failed !',
    'save_succeeded'        => 'Save succeeded !',
    'refresh_succeeded'     => 'Refresh succeeded !',
    'login_successful'      => 'Login successful',
    'choose'                => 'Choose',
    'choose_file'           => 'Select file',
    'choose_image'          => 'Select image',
    'more'                  => 'More',
    'deny'                  => 'Permission denied',
    'administrator'         => 'Administrator',
    'no_data'               => 'No data.',
    'roles'                 => 'Roles',
    'permissions'           => 'Permissions',
    'slug'                  => 'Slug',
    'created_at'            => 'Created At',
    'updated_at'            => 'Updated At',
    'alert'                 => 'Alert',
    'parent_id'             => 'Parent',
    'icon'                  => 'Icon',
    'uri'                   => 'URI',
    'operation_log'         => 'Operation log',
    'parent_select_error'   => 'Parent select error',
    'tree'                  => 'Tree',
    'table'                 => 'Table',
    'default'               => 'Default',
    'import'                => 'Import',
    'is_not_import'         => 'No',
    'selected_options'      => ':num options selected',
    'pagination'            => [
        'range' => 'Showing :first to :last of :total entries',
    ],
    'role'                  => 'Role',
    'permission'            => 'Permission',
    'route'                 => 'Route',
    'confirm'               => 'Confirm',
    'cancel'                => 'Cancel',
    'selectall'             => 'Select all',
    'http'                  => [
        'method' => 'HTTP method',
        'path'   => 'HTTP path',
    ],
    'all_methods_if_empty'  => 'All methods if empty',
    'all'                   => 'All',
    'current_page'          => 'Current page',
    'selected_rows'         => 'Selected rows',
    'upload'                => 'Upload',
    'new_folder'            => 'New folder',
    'time'                  => 'Time',
    'size'                  => 'Size',
    'between_start'         => 'Start',
    'between_end'           => 'End',
    'next_page'             => 'Next',
    'prev_page'             => 'Previous',
    'next_step'             => 'Next',
    'prev_step'             => 'Previous',
    'done'                  => 'Done',
    'listbox'               => [
        'text_total'         => 'Showing all {0}',
        'text_empty'         => 'Empty list',
        'filtered'           => '{0} / {1}',
        'filter_clear'       => 'Show all',
        'filter_placeholder' => 'Filter',
    ],
    'responsive' => [
        'display_all' => 'Display all',
        'display'     => 'Display',
        'focus'       => 'Focus',
    ],
    'uploader' => [
        'add_new_media'          => 'Browse',
        'drag_file'              => 'Or drag file here',
        'max_file_limit'         => 'The :attribute may not be greater than :max.',
        'exceed_size'            => 'Exceeds the maximum file-size',
        'interrupt'              => 'Interrupt',
        'upload_failed'          => 'Upload failed! Please try again.',
        'selected_files'         => ':num files selected，size: :size。',
        'selected_has_failed'    => 'Uploaded: :success, failed: :fail, <a class="retry"  href="javascript:"";">retry </a>or<a class="ignore" href="javascript:"";"> ignore</a>',
        'selected_success'       => ':num(:size) files selected, Uploaded: :success.',
        'dot'                    => ', ',
        'failed_num'             => 'failed::fail.',
        'pause_upload'           => 'Pause',
        'go_on_upload'           => 'Go On',
        'start_upload'           => 'Upload',
        'upload_success_message' => ':success files uploaded successfully',
        'go_on_add'              => 'New File',
        'Q_TYPE_DENIED'          => 'Sorry, the type of this file is not allowed!',
        'Q_EXCEED_NUM_LIMIT'     => 'Sorry, maximum number of allowable file uploads has been exceeded!',
        'F_EXCEED_SIZE'          => 'Sorry，the maximum file-size has been exceeded!',
        'Q_EXCEED_SIZE_LIMIT'    => 'Sorry, the maximum file-size has been exceeded!',
        'F_DUPLICATE'            => 'Duplicate file.',
        'confirm_delete_file'    => 'Are you sure delete this file from server?',
    ],
    'import_extension_confirm' => 'Are you sure import the extension?',
    'quick_create'             => 'Quick create',
    'grid_items_selected'      => '{n} items selected',
    'nothing_updated'          => 'Nothing has been updated.',
    'welcome_back'             => 'Welcome back, please login to your account.',
    'documentation'            => 'Documentation',
    'demo'                     => 'Demo',
    'extensions'               => 'Extensions',
    'validation'               => [
        'match'     => 'The :attribute and :other must match.',
        'minlength' => 'The :attribute must be at least :min characters.',
        'maxlength' => 'The :attribute may not be greater than :max characters.',
    ],
    'menu_titles' => [],
];
