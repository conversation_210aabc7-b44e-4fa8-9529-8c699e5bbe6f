{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.3", "ext-curl": "*", "ext-json": "*", "astrotomic/laravel-translatable": "^11.8", "bacon/bacon-qr-code": "~1.0.3", "bitwasp/bitcoin": "^1.0", "changzhong/extension-iframe-tabs": "^1.0", "dcat-admin-extension/ueditor": "dev-master", "dcat/easy-excel": "^1.0", "dcat/laravel-admin": "^1.7", "denpa/php-bitcoinrpc": "^2.1", "dingo/api": "^2.3", "fideloper/proxy": "^4.0", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "7.0.1", "iexbase/tron-api": "^3.1", "jenssegers/agent": "^2.6", "jenssegers/mongodb": "^3.6", "laravel/framework": "6.*", "laravel/helpers": "^1.2", "laravel/tinker": "^1.0", "medz/id-card-of-china": "^1.1", "overtrue/easy-sms": "^1.1", "pragmarx/google2fa-laravel": "^1.3", "predis/predis": "^1.1", "swoole/ide-helper": "^4.5", "symfony/console": "4.3.4", "torann/geoip": "^1.2", "tymon/jwt-auth": "^1.0.0", "web3p/ethereum-tx": "dev-master", "workerman/channel": "^1.0", "workerman/gateway-worker": "^3.0", "workerman/gatewayclient": "^3.0", "workerman/http-client": "^0.1.6", "zgldh/qiniu-laravel-storage": "^0.10.3"}, "require-dev": {"beyondcode/laravel-dump-server": "^1.0", "filp/whoops": "^2.0", "fzaninotto/faker": "^1.4", "mockery/mockery": "^1.0", "nunomaduro/collision": "^3.0", "phpunit/phpunit": "^7.5"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "platform-check": false}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"], "files": ["app/Common/functions.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}