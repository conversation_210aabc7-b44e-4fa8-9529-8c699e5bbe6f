# 贷款功能系统分析报告

## 📋 功能概述

贷款功能是一个完整的借贷系统，包含前端用户申请、后台管理审核、产品配置等完整流程。

## 🎯 前端功能 (#/loan)

### 1. 主要页面路由
```javascript
// echo2.0-h5-new/src/router/module/loan.js
{
  path: '/loan',           // 助力贷主页
  path: '/loan-record',    // 助力贷记录
  path: '/loan-rule'       // 助力贷规则
}
```

### 2. 前端API接口
```javascript
// echo2.0-h5-new/src/api/loan.js
getLoanProductList()     // 查询借贷产品
getLoan(params)          // 提交借贷订单
getLoanOrderList(params) // 查询借贷订单列表
```

### 3. 前端功能流程

**申请贷款页面 (/loan):**
- 选择贷款产品（金额范围、周期、利率）
- 输入贷款金额
- 上传身份证正面、反面、手持身份证照片
- 提交申请

**贷款记录页面 (/loan-record):**
- 显示贷款订单列表
- 支持状态筛选：全部、待审核、审核成功、审核失败、已结清、已逾期
- 显示订单详情：金额、状态、周期、利率、利息等

## 🏢 后台管理功能

### 1. 贷款产品管理 (#/loan/product)

**页面位置:** `echo2.0-admin/src/views/loan/product/index.vue`

**功能特性:**
- 产品列表查询（支持金额范围筛选）
- 新增/编辑/删除贷款产品
- 产品状态管理（开启/关闭）
- 冻结状态管理

**产品配置字段:**
- 最小/最大贷款额度 (USDT)
- 贷款周期 (天数)
- 日利率 (%)
- 还款类型 (到期一次还本息)
- 还款机构
- 状态控制
- 冻结控制

### 2. 贷款订单管理 (#/loan/order)

**页面位置:** `echo2.0-admin/src/views/loan/order/index.vue`

**功能特性:**
- 订单列表查询
- 订单审核（通过/拒绝）
- 查看订单详情
- 还款操作
- 逾期管理

**订单状态:**
- 0: 待审核
- 1: 审核通过
- 2: 审核拒绝  
- 3: 已结清
- 4: 已逾期

## 🔧 后台API实现

### 1. 贷款产品API

**管理端Controller:** `TLoadProductController.java`
```java
// 路径: /bussiness/load/product
GET  /list        // 查询产品列表
GET  /{id}        // 获取产品详情
POST /            // 新增产品
PUT  /            // 修改产品
DELETE /{ids}     // 删除产品
```

**用户端Controller:** `TLoadProductController.java` (API端)
```java
// 路径: /api/load/product
POST /list        // 查询可用产品列表
```

### 2. 贷款订单API

**管理端Controller:** `TLoadOrderController.java`
```java
// 路径: /bussiness/load/order
GET  /orderList              // 订单列表（含逾期计算）
POST /passTLoadOrder         // 审核通过
POST /refuseTLoadOrder       // 审核拒绝
GET  /getTLoadOrder/{id}     // 查看订单详情
POST /repayment             // 还款操作
```

**用户端Controller:** `TLoadOrderController.java` (API端)
```java
// 路径: /api/load/order
POST /orderList     // 用户订单列表
POST /submit        // 提交贷款申请
```

## 🗄️ 数据库表结构

### 1. 贷款产品表 (t_load_product)

```sql
CREATE TABLE t_load_product (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  amount_min DECIMAL(10,2),      -- 最小贷款额度
  amount_max DECIMAL(10,2),      -- 最大贷款额度
  cycle_type BIGINT,             -- 周期类型(天数)
  repay_type BIGINT,             -- 还款类型
  status BIGINT,                 -- 状态(0未开启 1已开启)
  odds DECIMAL(10,2),            -- 日利率(%)
  repay_org VARCHAR(255),        -- 还款机构
  is_freeze VARCHAR(10),         -- 是否冻结(1正常 2冻结)
  create_time DATETIME,
  update_time DATETIME,
  remark TEXT
);
```

### 2. 贷款订单表 (t_load_order)

```sql
CREATE TABLE t_load_order (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  pro_id BIGINT,                 -- 贷款产品ID
  user_id BIGINT,                -- 用户ID
  amount DECIMAL(10,2),          -- 申请金额
  rate DECIMAL(10,2),            -- 贷款利率
  interest DECIMAL(10,2),        -- 利息
  status INT,                    -- 状态(0待审核 1通过 2拒绝 3结清 4逾期)
  final_repay_time DATETIME,     -- 最后还款日
  disburse_time DATETIME,        -- 放款日期
  return_time DATETIME,          -- 还款日期
  disburse_amount DECIMAL(10,2), -- 审批金额
  admin_parent_ids VARCHAR(255), -- 后台代理IDs
  card_url VARCHAR(255),         -- 身份证正面
  card_back_url VARCHAR(255),    -- 身份证反面
  capital_url VARCHAR(255),      -- 手持身份证
  order_no VARCHAR(50),          -- 订单号
  cycle_type INT,                -- 还款周期
  create_time DATETIME,
  update_time DATETIME
);
```

## 💼 业务逻辑流程

### 1. 贷款申请流程

1. **用户选择产品:** 前端调用 `/api/load/product/list` 获取可用产品
2. **填写申请信息:** 用户输入金额、上传身份证照片
3. **提交申请:** 调用 `/api/load/order/submit` 创建订单
4. **系统处理:** 
   - 生成订单号 (Z + 时间戳)
   - 计算利息 = 金额 × 日利率 × 周期天数 ÷ 100
   - 设置状态为待审核(0)

### 2. 审核流程

1. **管理员查看:** 后台 `/bussiness/load/order/orderList` 查看待审核订单
2. **审核决策:**
   - **通过:** 调用 `/passTLoadOrder`
     - 设置状态为审核通过(1)
     - 计算最后还款日期
     - 向用户USDT账户增加审批金额
     - 生成钱包记录
     - 冻结用户状态
   - **拒绝:** 调用 `/refuseTLoadOrder` 设置状态为拒绝(2)

### 3. 逾期管理

**自动逾期检测:**
- 系统自动检查 `final_repay_time` 与当前时间
- 超期自动将状态从 1(审核通过) 改为 4(已逾期)
- 计算逾期利息 = 审批金额 × 逾期天数 × 逾期利率

**逾期利率配置:**
- 存储在系统设置中 (`LOAD_SETTING`)
- 默认逾期利率: 2.5% (0.025)

### 4. 还款流程

1. **管理员操作:** 调用 `/repayment` 接口
2. **系统处理:**
   - 设置状态为已结清(3)
   - 记录还款时间
   - 检查用户是否还有其他未结清贷款
   - 如无其他贷款，解除用户冻结状态

## 🔧 核心配置

### 1. 贷款设置 (LoadSetting)

```java
// 存储在系统设置表中，key: LOAD_SETTING
{
  "overdueRate": "0.025"  // 逾期利率 2.5%
}
```

### 2. 还款类型字典 (t_repay_type)

```
0: 到期一次还本息
```

## 📊 关键计算公式

### 1. 利息计算
```
利息 = 贷款金额 × 日利率(%) × 贷款周期(天) ÷ 100
```

### 2. 逾期利息计算  
```
逾期利息 = 审批金额 × 逾期天数 × 逾期利率
```

## 🔐 权限控制

**前端路由权限:**
- `/loan` 需要登录访问 (hasLoginRouterList)

**后台接口权限:**
- `bussiness:load:product:*` - 产品管理权限
- `bussiness:load:order:*` - 订单管理权限

## 📱 前端组件结构

```
src/views/loan/
├── index.vue              // 贷款申请主页
├── loan-record.vue        // 贷款记录页面
├── loan-rule.vue          // 贷款规则页面
└── components/
    └── LoanItem.vue       // 贷款记录项组件
```

## 🎯 总结

贷款功能是一个完整的借贷管理系统，包含：
- ✅ 产品配置管理
- ✅ 用户申请流程  
- ✅ 审核工作流
- ✅ 自动逾期检测
- ✅ 还款管理
- ✅ 资金流转记录
- ✅ 权限控制

系统设计合理，业务流程完整，支持完整的借贷业务场景。
