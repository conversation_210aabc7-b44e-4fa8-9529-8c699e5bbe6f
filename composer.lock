{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "3683d5b7b8d2fac50a7a5c8075a17adb", "packages": [{"name": "asm89/stack-cors", "version": "v2.0.3", "source": {"type": "git", "url": "https://github.com/asm89/stack-cors.git", "reference": "9cb795bf30988e8c96dd3c40623c48a877bc6714"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/asm89/stack-cors/zipball/9cb795bf30988e8c96dd3c40623c48a877bc6714", "reference": "9cb795bf30988e8c96dd3c40623c48a877bc6714", "shasum": ""}, "require": {"php": "^7.0|^8.0", "symfony/http-foundation": "~2.7|~3.0|~4.0|~5.0", "symfony/http-kernel": "~2.7|~3.0|~4.0|~5.0"}, "require-dev": {"phpunit/phpunit": "^6|^7|^8|^9", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"Asm89\\Stack\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Cross-origin resource sharing library and stack middleware", "homepage": "https://github.com/asm89/stack-cors", "keywords": ["cors", "stack"], "support": {"issues": "https://github.com/asm89/stack-cors/issues", "source": "https://github.com/asm89/stack-cors/tree/v2.0.3"}, "time": "2021-03-11T06:42:03+00:00"}, {"name": "astrotomic/laravel-translatable", "version": "v11.9.1", "source": {"type": "git", "url": "https://github.com/Astrotomic/laravel-translatable.git", "reference": "d853a3c34be42941dc83c5cddd9e1e98c71abae1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Astrotomic/laravel-translatable/zipball/d853a3c34be42941dc83c5cddd9e1e98c71abae1", "reference": "d853a3c34be42941dc83c5cddd9e1e98c71abae1", "shasum": ""}, "require": {"illuminate/contracts": "5.8.* || ^6.0 || ^7.0 || ^8.0", "illuminate/database": "5.8.* || ^6.0 || ^7.0 || ^8.0", "illuminate/support": "5.8.* || ^6.0 || ^7.0 || ^8.0", "php": ">=7.2"}, "require-dev": {"orchestra/testbench": "3.8.* || ^4.0 || ^5.0 || ^6.0", "phpunit/phpunit": "^8.0 || ^9.0"}, "type": "library", "extra": {"laravel": {"providers": ["Astrotomic\\Translatable\\TranslatableServiceProvider"]}}, "autoload": {"psr-4": {"Astrotomic\\Translatable\\": "src/Translatable/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://gummibeer.de", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://dimsav.com", "role": "Developer"}], "description": "A Laravel package for multilingual models", "homepage": "https://astrotomic.info", "keywords": ["database", "language", "laravel", "translation"], "support": {"docs": "https://docs.astrotomic.info/laravel-translatable", "email": "<EMAIL>", "issues": "https://github.com/Astrotomic/laravel-translatable/issues", "source": "https://github.com/Astrotomic/laravel-translatable"}, "funding": [{"url": "https://offset.earth/treeware", "type": "custom"}, {"url": "https://github.com/Gummibeer", "type": "github"}, {"url": "https://issuehunt.io/r/astrotomic", "type": "issuehunt"}], "time": "2020-11-19T14:10:38+00:00"}, {"name": "bacon/bacon-qr-code", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/Bacon/BaconQrCode.git", "reference": "5a91b62b9d37cee635bbf8d553f4546057250bee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Bacon/BaconQrCode/zipball/5a91b62b9d37cee635bbf8d553f4546057250bee", "reference": "5a91b62b9d37cee635bbf8d553f4546057250bee", "shasum": ""}, "require": {"ext-iconv": "*", "php": "^5.4|^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8"}, "suggest": {"ext-gd": "to generate QR code images"}, "type": "library", "autoload": {"psr-0": {"BaconQrCode": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "http://www.dasprids.de", "role": "Developer"}], "description": "BaconQrCode is a QR code generator for PHP.", "homepage": "https://github.com/Bacon/BaconQrCode", "support": {"issues": "https://github.com/Bacon/BaconQrCode/issues", "source": "https://github.com/Bacon/BaconQrCode/tree/master"}, "time": "2017-10-17T09:59:25+00:00"}, {"name": "bitwasp/bech32", "version": "v0.0.1", "source": {"type": "git", "url": "https://github.com/Bit-Wasp/bech32.git", "reference": "e1ea58c848a4ec59d81b697b3dfe9cc99968d0e7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Bit-Wasp/bech32/zipball/e1ea58c848a4ec59d81b697b3dfe9cc99968d0e7", "reference": "e1ea58c848a4ec59d81b697b3dfe9cc99968d0e7", "shasum": ""}, "require-dev": {"phpunit/phpunit": "^5.4.0", "squizlabs/php_codesniffer": "^2.0.0"}, "type": "library", "autoload": {"psr-4": {"BitWasp\\Bech32\\": "src/"}, "files": ["src/bech32.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Unlicense"], "authors": [{"name": "<PERSON>", "homepage": "https://thomaskerin.io", "role": "Author"}], "description": "Pure (no dependencies) implementation of bech32", "homepage": "https://github.com/bit-wasp/bech32", "support": {"issues": "https://github.com/Bit-Wasp/bech32/issues", "source": "https://github.com/Bit-Wasp/bech32/tree/more-tests"}, "time": "2018-02-05T22:23:47+00:00"}, {"name": "bitwasp/bitcoin", "version": "v1.0.4", "source": {"type": "git", "url": "https://github.com/Bit-Wasp/bitcoin-php.git", "reference": "65ff8384a15e805effcf600fb08cef3a0fc63824"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Bit-Wasp/bitcoin-php/zipball/65ff8384a15e805effcf600fb08cef3a0fc63824", "reference": "65ff8384a15e805effcf600fb08cef3a0fc63824", "shasum": ""}, "require": {"bitwasp/bech32": "^0.0.1", "bitwasp/buffertools": "^0.5.0", "composer/semver": "^1.4.0", "lastguest/murmurhash": "v2.0.0", "mdanter/ecc": "^0.5.0", "php-64bit": ">=7.0", "pleonasm/merkle-tree": "1.0.0"}, "require-dev": {"bitwasp/bitcoinconsensus": "v3.0.0", "bitwasp/secp256k1-php": "^v0.2.0", "ext-json": "*", "nbobtc/bitcoind-php": "v2.0.2", "phpunit/phpunit": "^5.4.0", "squizlabs/php_codesniffer": "^2.0.0"}, "suggest": {"ext-bitcoinconsensus": "The bitcoinconsensus library for safest possible script verification", "ext-secp256k1": "The secp256k1 library for fast and safe elliptic curve operations"}, "type": "library", "autoload": {"psr-4": {"BitWasp\\Bitcoin\\": "src/"}, "files": ["src/Script/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Unlicense"], "authors": [{"name": "<PERSON>", "homepage": "https://thomaskerin.io", "role": "Author"}], "description": "PHP Bitcoin library with functions for transactions, signatures, serialization, Random/Deterministic ECDSA keys, blocks, RPC bindings", "homepage": "https://github.com/bit-wasp/bitcoin-php", "support": {"issues": "https://github.com/Bit-Wasp/bitcoin-php/issues", "source": "https://github.com/Bit-Wasp/bitcoin-php/tree/v1.0.4"}, "time": "2019-12-10T23:28:26+00:00"}, {"name": "bitwasp/buffertools", "version": "v0.5.7", "source": {"type": "git", "url": "https://github.com/Bit-Wasp/buffertools-php.git", "reference": "133746d0b514e0016d8479b54aa97475405a9f1f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Bit-Wasp/buffertools-php/zipball/133746d0b514e0016d8479b54aa97475405a9f1f", "reference": "133746d0b514e0016d8479b54aa97475405a9f1f", "shasum": ""}, "require": {"php-64bit": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "^6.0", "squizlabs/php_codesniffer": "~2.0"}, "type": "library", "autoload": {"psr-4": {"BitWasp\\Buffertools\\": "src/Buffertools/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://thomaskerin.io"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Toolbox for working with binary and hex data. Similar to NodeJS Buffer.", "support": {"issues": "https://github.com/Bit-Wasp/buffertools-php/issues", "source": "https://github.com/Bit-Wasp/buffertools-php/tree/v0.5.7"}, "time": "2020-01-17T21:31:49+00:00"}, {"name": "box/spout", "version": "v3.3.0", "source": {"type": "git", "url": "https://github.com/box/spout.git", "reference": "9bdb027d312b732515b884a341c0ad70372c6295"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/box/spout/zipball/9bdb027d312b732515b884a341c0ad70372c6295", "reference": "9bdb027d312b732515b884a341c0ad70372c6295", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlreader": "*", "ext-zip": "*", "php": ">=7.2.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2", "phpunit/phpunit": "^8"}, "suggest": {"ext-iconv": "To handle non UTF-8 CSV files (if \"php-intl\" is not already installed or is too limited)", "ext-intl": "To handle non UTF-8 CSV files (if \"iconv\" is not already installed)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"psr-4": {"Box\\Spout\\": "src/Spout"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP Library to read and write spreadsheet files (CSV, XLSX and ODS), in a fast and scalable way", "homepage": "https://www.github.com/box/spout", "keywords": ["OOXML", "csv", "excel", "memory", "odf", "ods", "office", "open", "php", "read", "scale", "spreadsheet", "stream", "write", "xlsx"], "support": {"issues": "https://github.com/box/spout/issues", "source": "https://github.com/box/spout/tree/v3.3.0"}, "time": "2021-05-14T21:18:09+00:00"}, {"name": "changzhong/extension-iframe-tabs", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/changzhong/extension-iframe-tabs.git", "reference": "1b9f42e073b297eecf148526fc2d9187defffa9f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/changzhong/extension-iframe-tabs/zipball/1b9f42e073b297eecf148526fc2d9187defffa9f", "reference": "1b9f42e073b297eecf148526fc2d9187defffa9f", "shasum": ""}, "require": {"dcat/laravel-admin": "^1.7.0", "php": ">=7.1.0"}, "type": "library", "autoload": {"psr-4": {"Dcat\\Admin\\Extension\\IframeTabs\\": "src/"}, "files": ["src/bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "chang", "email": "<EMAIL>"}], "description": "dcat-admin extension iframe-tabs", "homepage": "https://github.com/changzhong/extension-iframe-tabs", "keywords": ["dcat-admin", "extension"], "support": {"issues": "https://github.com/changzhong/extension-iframe-tabs/issues", "source": "https://github.com/changzhong/extension-iframe-tabs/tree/master"}, "time": "2020-09-04T09:52:41+00:00"}, {"name": "composer/package-versions-deprecated", "version": "*********", "source": {"type": "git", "url": "https://github.com/composer/package-versions-deprecated.git", "reference": "b174585d1fe49ceed21928a945138948cb394600"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/package-versions-deprecated/zipball/b174585d1fe49ceed21928a945138948cb394600", "reference": "b174585d1fe49ceed21928a945138948cb394600", "shasum": ""}, "require": {"composer-plugin-api": "^1.1.0 || ^2.0", "php": "^7 || ^8"}, "replace": {"ocramius/package-versions": "1.11.99"}, "require-dev": {"composer/composer": "^1.9.3 || ^2.0@dev", "ext-zip": "^1.13", "phpunit/phpunit": "^6.5 || ^7"}, "type": "composer-plugin", "extra": {"class": "PackageVersions\\Installer", "branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"PackageVersions\\": "src/PackageVersions"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "Composer plugin that provides efficient querying for installed package versions (no runtime IO)", "support": {"issues": "https://github.com/composer/package-versions-deprecated/issues", "source": "https://github.com/composer/package-versions-deprecated/tree/*********"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2021-09-13T08:41:34+00:00"}, {"name": "composer/semver", "version": "1.7.2", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "647490bbcaf7fc4891c58f47b825eb99d19c377a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/647490bbcaf7fc4891c58f47b825eb99d19c377a", "reference": "647490bbcaf7fc4891c58f47b825eb99d19c377a", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.5 || ^5.0.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/1.7.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2020-12-03T15:47:16+00:00"}, {"name": "dcat-admin-extension/ueditor", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/jqhph/dcat-admin-ueditor.git", "reference": "2df6828076d64ba1ea4c5937bcf5d0b982769f06"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jqhph/dcat-admin-ueditor/zipball/2df6828076d64ba1ea4c5937bcf5d0b982769f06", "reference": "2df6828076d64ba1ea4c5937bcf5d0b982769f06", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"phpunit/phpunit": "~6.0"}, "default-branch": true, "type": "library", "autoload": {"psr-4": {"Dcat\\Admin\\Extension\\UEditor\\": "src/"}, "files": ["src/bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "jqh", "email": "<EMAIL>"}], "description": "百度在线编辑器", "homepage": "https://github.com/jqhph", "keywords": ["dcat-admin", "extension"], "support": {"issues": "https://github.com/jqhph/dcat-admin-ueditor/issues", "source": "https://github.com/jqhph/dcat-admin-ueditor/tree/master"}, "time": "2021-02-23T08:00:50+00:00"}, {"name": "dcat/easy-excel", "version": "1.0.6", "source": {"type": "git", "url": "https://github.com/jqhph/easy-excel.git", "reference": "a80d46001449a43b80700e3fe29e3f047cfc489d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jqhph/easy-excel/zipball/a80d46001449a43b80700e3fe29e3f047cfc489d", "reference": "a80d46001449a43b80700e3fe29e3f047cfc489d", "shasum": ""}, "require": {"box/spout": "~3", "league/flysystem": "~1|~2", "php": ">=7.1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2", "phpunit/phpunit": "~7|~8.0"}, "type": "library", "autoload": {"psr-4": {"Dcat\\EasyExcel\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "jqh", "email": "<EMAIL>"}], "description": "使用简单实用的语义化接口快速读写Excel文件", "homepage": "https://github.com/jqhph/easy-excel", "keywords": ["box spout", "csv", "easy excel", "excel", "ods", "office", "read", "spreadsheet", "stream", "xlsx"], "support": {"issues": "https://github.com/jqhph/easy-excel/issues", "source": "https://github.com/jqhph/easy-excel/tree/1.0.6"}, "time": "2021-09-14T05:52:08+00:00"}, {"name": "dcat/laravel-admin", "version": "1.7.9", "source": {"type": "git", "url": "https://github.com/jqhph/dcat-admin.git", "reference": "dabd5f14133e7b7e906b57aa4f7ed536ee052ead"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jqhph/dcat-admin/zipball/dabd5f14133e7b7e906b57aa4f7ed536ee052ead", "reference": "dabd5f14133e7b7e906b57aa4f7ed536ee052ead", "shasum": ""}, "require": {"doctrine/dbal": "2.*", "laravel/framework": "~5.5|~6.0|~7.0|~8.0", "php": ">=7.1.0", "spatie/eloquent-sortable": "3.*", "symfony/dom-crawler": "~3.1|~4.0|~5.0"}, "require-dev": {"fzaninotto/faker": "^1.4", "laravel/dusk": "~5.9", "mockery/mockery": "^1.0", "phpstan/phpstan": "^0.12.0", "phpunit/phpunit": "^7.5"}, "type": "library", "extra": {"laravel": {"providers": ["Dcat\\Admin\\AdminServiceProvider"]}}, "autoload": {"psr-4": {"Dcat\\Admin\\": "src/"}, "files": ["src/Support/helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "jqh", "email": "<EMAIL>"}], "description": "dcat admin", "homepage": "https://github.com/jqhph/dcat-admin", "keywords": ["admin", "dcat", "form", "grid", "laravel", "laravel admin"], "support": {"issues": "https://github.com/jqhph/dcat-admin/issues", "source": "https://github.com/jqhph/dcat-admin/tree/1.7.9"}, "time": "2020-12-24T13:51:01+00:00"}, {"name": "denpa/php-bitcoinrpc", "version": "v2.1.3", "source": {"type": "git", "url": "https://github.com/denpamusic/php-bitcoinrpc.git", "reference": "578dab84e713225b91105c54ee3485a078285788"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/denpamusic/php-bitcoinrpc/zipball/578dab84e713225b91105c54ee3485a078285788", "reference": "578dab84e713225b91105c54ee3485a078285788", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.5 | ^7.0", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.0 | ^8.0"}, "type": "library", "autoload": {"psr-4": {"Denpa\\Bitcoin\\": "src"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Bitcoin JSON-RPC client based on GuzzleHttp", "homepage": "https://github.com/denpamusic/php-bitcoinrpc", "keywords": ["Guzzle", "api", "bitcoin", "jsonrpc"], "support": {"issues": "https://github.com/denpamusic/php-bitcoinrpc/issues", "source": "https://github.com/denpamusic/php-bitcoinrpc/tree/2.1.x"}, "time": "2020-08-23T04:32:56+00:00"}, {"name": "dingo/api", "version": "v2.4.7", "source": {"type": "git", "url": "https://github.com/dingo/api.git", "reference": "669a5a9f39cf4f499af1b6fcf43c15e364312ad3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dingo/api/zipball/669a5a9f39cf4f499af1b6fcf43c15e364312ad3", "reference": "669a5a9f39cf4f499af1b6fcf43c15e364312ad3", "shasum": ""}, "require": {"dingo/blueprint": "^0.3", "illuminate/routing": "^5.5 || ^6.0", "illuminate/support": "^5.5 || ^6.0", "league/fractal": "^0.17", "php": "^7.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2", "illuminate/auth": "^5.5 || ^6.0", "illuminate/cache": "^5.5 || ^6.0", "illuminate/console": "^5.5 || ^6.0", "illuminate/database": "^5.5 || ^6.0", "illuminate/events": "^5.5 || ^6.0", "illuminate/filesystem": "^5.5 || ^6.0", "illuminate/log": "^5.5 || ^6.0", "illuminate/pagination": "^5.5 || ^6.0", "laravel/lumen-framework": "^5.5 || ^6.0", "mockery/mockery": "~1.0", "phpdocumentor/reflection-docblock": "3.3.2", "phpunit/phpunit": "^4.8.35 || ^5.4.3 || ^6.5", "squizlabs/php_codesniffer": "~2.0", "tymon/jwt-auth": "1.0.*"}, "suggest": {"tymon/jwt-auth": "Protect your API with JSON Web Tokens."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}, "laravel": {"providers": ["Dingo\\Api\\Provider\\LaravelServiceProvider"], "aliases": {"API": "Dingo\\Api\\Facade\\API"}}}, "autoload": {"psr-4": {"Dingo\\Api\\": "src/"}, "files": ["src/helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A RESTful API package for the Laravel and Lumen frameworks.", "keywords": ["api", "dingo", "laravel", "restful"], "support": {"issues": "https://github.com/dingo/api/issues", "source": "https://github.com/dingo/api/tree/v2"}, "time": "2020-03-19T01:36:11+00:00"}, {"name": "dingo/blueprint", "version": "v0.3.1", "source": {"type": "git", "url": "https://github.com/dingo/blueprint.git", "reference": "45bbc59385310de7604e35ea4e27dc1756be9396"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dingo/blueprint/zipball/45bbc59385310de7604e35ea4e27dc1756be9396", "reference": "45bbc59385310de7604e35ea4e27dc1756be9396", "shasum": ""}, "require": {"doctrine/annotations": "~1.2", "illuminate/filesystem": "^5.5 || ^6.0", "illuminate/support": "^5.5 || ^6.0", "php": "^7.1", "phpdocumentor/reflection-docblock": "^3.1|^4.1"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.4.3 || ^6.5", "squizlabs/php_codesniffer": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.2-dev"}}, "autoload": {"psr-4": {"Dingo\\Blueprint\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "API Blueprint documentation generator.", "keywords": ["api", "blueprint", "dingo", "docs", "laravel"], "support": {"issues": "https://github.com/dingo/blueprint/issues", "source": "https://github.com/dingo/blueprint/tree/v3.0.1"}, "time": "2019-10-07T10:14:17+00:00"}, {"name": "dnoegel/php-xdg-base-dir", "version": "v0.1.1", "source": {"type": "git", "url": "https://github.com/dnoegel/php-xdg-base-dir.git", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dnoegel/php-xdg-base-dir/zipball/8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "~7.0|~6.0|~5.0|~4.8.35"}, "type": "library", "autoload": {"psr-4": {"XdgBaseDir\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "implementation of xdg base directory specification for php", "support": {"issues": "https://github.com/dnoegel/php-xdg-base-dir/issues", "source": "https://github.com/dnoegel/php-xdg-base-dir/tree/v0.1.1"}, "time": "2019-12-04T15:06:13+00:00"}, {"name": "doctrine/annotations", "version": "1.13.2", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "5b668aef16090008790395c02c893b1ba13f7e08"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/5b668aef16090008790395c02c893b1ba13f7e08", "reference": "5b668aef16090008790395c02c893b1ba13f7e08", "shasum": ""}, "require": {"doctrine/lexer": "1.*", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^6.0 || ^8.1", "phpstan/phpstan": "^0.12.20", "phpunit/phpunit": "^7.5 || ^8.0 || ^9.1.5", "symfony/cache": "^4.4 || ^5.2"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.13.2"}, "time": "2021-08-05T19:00:23+00:00"}, {"name": "doctrine/cache", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "331b4d5dbaeab3827976273e9356b3b453c300ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/331b4d5dbaeab3827976273e9356b3b453c300ce", "reference": "331b4d5dbaeab3827976273e9356b3b453c300ce", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "cache/integration-tests": "dev-master", "doctrine/coding-standard": "^8.0", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "predis/predis": "~1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.2 || ^6.0@dev", "symfony/var-exporter": "^4.4 || ^5.2 || ^6.0@dev"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.1.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2021-07-17T14:49:29+00:00"}, {"name": "doctrine/dbal", "version": "2.13.6", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "67ef6d0327ccbab1202b39e0222977a47ed3ef2f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/67ef6d0327ccbab1202b39e0222977a47ed3ef2f", "reference": "67ef6d0327ccbab1202b39e0222977a47ed3ef2f", "shasum": ""}, "require": {"doctrine/cache": "^1.0|^2.0", "doctrine/deprecations": "^0.5.3", "doctrine/event-manager": "^1.0", "ext-pdo": "*", "php": "^7.1 || ^8"}, "require-dev": {"doctrine/coding-standard": "9.0.0", "jetbrains/phpstorm-stubs": "2021.1", "phpstan/phpstan": "1.2.0", "phpunit/phpunit": "^7.5.20|^8.5|9.5.10", "psalm/plugin-phpunit": "0.16.1", "squizlabs/php_codesniffer": "3.6.1", "symfony/cache": "^4.4", "symfony/console": "^2.0.5|^3.0|^4.0|^5.0", "vimeo/psalm": "4.13.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "lib/Doctrine/DBAL"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlanywhere", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/2.13.6"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2021-11-26T20:11:05+00:00"}, {"name": "doctrine/deprecations", "version": "v0.5.3", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "9504165960a1f83cc1480e2be1dd0a0478561314"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/9504165960a1f83cc1480e2be1dd0a0478561314", "reference": "9504165960a1f83cc1480e2be1dd0a0478561314", "shasum": ""}, "require": {"php": "^7.1|^8.0"}, "require-dev": {"doctrine/coding-standard": "^6.0|^7.0|^8.0", "phpunit/phpunit": "^7.0|^8.0|^9.0", "psr/log": "^1.0"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/v0.5.3"}, "time": "2021-03-21T12:59:47+00:00"}, {"name": "doctrine/event-manager", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/41370af6a30faa9dc0368c4a6814d596e81aba7f", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.1.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2020-05-29T18:28:51+00:00"}, {"name": "doctrine/inflector", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "8b7ff3e4b7de6b2c84da85637b59fd2880ecaa89"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/8b7ff3e4b7de6b2c84da85637b59fd2880ecaa89", "reference": "8b7ff3e4b7de6b2c84da85637b59fd2880ecaa89", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.2", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "vimeo/psalm": "^4.10"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.4"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2021-10-22T20:16:43+00:00"}, {"name": "doctrine/lexer", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "e864bbf5904cb8f5bb334f99209b48018522f042"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/e864bbf5904cb8f5bb334f99209b48018522f042", "reference": "e864bbf5904cb8f5bb334f99209b48018522f042", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11.8", "phpunit/phpunit": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2020-05-25T17:44:05+00:00"}, {"name": "dragonmantank/cron-expression", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/dragonmantank/cron-expression.git", "reference": "65b2d8ee1f10915efb3b55597da3404f096acba2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/65b2d8ee1f10915efb3b55597da3404f096acba2", "reference": "65b2d8ee1f10915efb3b55597da3404f096acba2", "shasum": ""}, "require": {"php": "^7.0|^8.0"}, "require-dev": {"phpunit/phpunit": "^6.4|^7.0|^8.0|^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v2.3.1"}, "funding": [{"url": "https://github.com/dragonmantank", "type": "github"}], "time": "2020-10-13T00:52:37+00:00"}, {"name": "egulias/email-validator", "version": "2.1.25", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "0dbf5d78455d4d6a41d186da50adc1122ec066f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/0dbf5d78455d4d6a41d186da50adc1122ec066f4", "reference": "0dbf5d78455d4d6a41d186da50adc1122ec066f4", "shasum": ""}, "require": {"doctrine/lexer": "^1.0.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.10"}, "require-dev": {"dominicsayers/isemail": "^3.0.7", "phpunit/phpunit": "^4.8.36|^7.5.15", "satooshi/php-coveralls": "^1.0.1"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/2.1.25"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2020-12-29T14:50:06+00:00"}, {"name": "fgrosse/phpasn1", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/fgrosse/PHPASN1.git", "reference": "eef488991d53e58e60c9554b09b1201ca5ba9296"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fgrosse/PHPASN1/zipball/eef488991d53e58e60c9554b09b1201ca5ba9296", "reference": "eef488991d53e58e60c9554b09b1201ca5ba9296", "shasum": ""}, "require": {"php": "~7.1.0 || ~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0"}, "require-dev": {"php-coveralls/php-coveralls": "~2.0", "phpunit/phpunit": "^6.3 || ^7.0 || ^8.0"}, "suggest": {"ext-bcmath": "BCmath is the fallback extension for big integer calculations", "ext-curl": "For loading OID information from the web if they have not bee defined statically", "ext-gmp": "GMP is the preferred extension for big integer calculations", "phpseclib/bcmath_compat": "BCmath polyfill for servers where neither GMP nor BCmath is available"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"FG\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Friedrich Große", "email": "<EMAIL>", "homepage": "https://github.com/FGrosse", "role": "Author"}, {"name": "All contributors", "homepage": "https://github.com/FGrosse/PHPASN1/contributors"}], "description": "A PHP Framework that allows you to encode and decode arbitrary ASN.1 structures using the ITU-T X.690 Encoding Rules.", "homepage": "https://github.com/FGrosse/PHPASN1", "keywords": ["DER", "asn.1", "asn1", "ber", "binary", "decoding", "encoding", "x.509", "x.690", "x509", "x690"], "support": {"issues": "https://github.com/fgrosse/PHPASN1/issues", "source": "https://github.com/fgrosse/PHPASN1/tree/v2.4.0"}, "time": "2021-12-11T12:41:06+00:00"}, {"name": "fideloper/proxy", "version": "4.4.1", "source": {"type": "git", "url": "https://github.com/fideloper/TrustedProxy.git", "reference": "c073b2bd04d1c90e04dc1b787662b558dd65ade0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fideloper/TrustedProxy/zipball/c073b2bd04d1c90e04dc1b787662b558dd65ade0", "reference": "c073b2bd04d1c90e04dc1b787662b558dd65ade0", "shasum": ""}, "require": {"illuminate/contracts": "^5.0|^6.0|^7.0|^8.0|^9.0", "php": ">=5.4.0"}, "require-dev": {"illuminate/http": "^5.0|^6.0|^7.0|^8.0|^9.0", "mockery/mockery": "^1.0", "phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"laravel": {"providers": ["Fideloper\\Proxy\\TrustedProxyServiceProvider"]}}, "autoload": {"psr-4": {"Fideloper\\Proxy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Set trusted proxies for <PERSON><PERSON>", "keywords": ["load balancing", "proxy", "trusted proxy"], "support": {"issues": "https://github.com/fideloper/TrustedProxy/issues", "source": "https://github.com/fideloper/TrustedProxy/tree/4.4.1"}, "time": "2020-10-22T13:48:01+00:00"}, {"name": "fruitcake/laravel-cors", "version": "v2.0.4", "source": {"type": "git", "url": "https://github.com/fruitcake/laravel-cors.git", "reference": "a8ccedc7ca95189ead0e407c43b530dc17791d6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fruitcake/laravel-cors/zipball/a8ccedc7ca95189ead0e407c43b530dc17791d6a", "reference": "a8ccedc7ca95189ead0e407c43b530dc17791d6a", "shasum": ""}, "require": {"asm89/stack-cors": "^2.0.1", "illuminate/contracts": "^6|^7|^8|^9", "illuminate/support": "^6|^7|^8|^9", "php": ">=7.2", "symfony/http-foundation": "^4|^5", "symfony/http-kernel": "^4.3.4|^5"}, "require-dev": {"laravel/framework": "^6|^7|^8", "orchestra/testbench-dusk": "^4|^5|^6|^7", "phpunit/phpunit": "^6|^7|^8|^9", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}, "laravel": {"providers": ["Fruitcake\\Cors\\CorsServiceProvider"]}}, "autoload": {"psr-4": {"Fruitcake\\Cors\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Fruitcake", "homepage": "https://fruitcake.nl"}, {"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "Adds CORS (Cross-Origin Resource Sharing) headers support in your Laravel application", "keywords": ["api", "cors", "crossdomain", "laravel"], "support": {"issues": "https://github.com/fruitcake/laravel-cors/issues", "source": "https://github.com/fruitcake/laravel-cors/tree/v2.0.4"}, "funding": [{"url": "https://github.com/barryvdh", "type": "github"}], "time": "2021-04-26T11:24:25+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.0.1", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "2d9d3c186a6637a43193e66b097c50e4451eaab2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/2d9d3c186a6637a43193e66b097c50e4451eaab2", "reference": "2d9d3c186a6637a43193e66b097c50e4451eaab2", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1", "php": "^7.2.5", "psr/http-client": "^1.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.0", "ext-curl": "*", "php-http/client-integration-tests": "dev-phpunit8", "phpunit/phpunit": "^8.5.5", "psr/log": "^1.1"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.0"}, "time": "2020-06-27T10:33:25+00:00"}, {"name": "guzzlehttp/promises", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "fe752aedc9fd8fcca3fe7ad05d419d32998a06da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/fe752aedc9fd8fcca3fe7ad05d419d32998a06da", "reference": "fe752aedc9fd8fcca3fe7ad05d419d32998a06da", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2021-10-22T20:56:57+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.8.3", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "1afdd860a2566ed3c2b0b4a3de6e23434a79ec85"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/1afdd860a2566ed3c2b0b4a3de6e23434a79ec85", "reference": "1afdd860a2566ed3c2b0b4a3de6e23434a79ec85", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/1.8.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2021-10-05T13:56:00+00:00"}, {"name": "iexbase/tron-api", "version": "v3.1.2", "source": {"type": "git", "url": "https://github.com/iexbase/tron-api.git", "reference": "fafeab39c4a7344aee8e8d222ce8f796c74d1df4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/iexbase/tron-api/zipball/fafeab39c4a7344aee8e8d222ce8f796c74d1df4", "reference": "fafeab39c4a7344aee8e8d222ce8f796c74d1df4", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^7.0", "iexbase/web3.php": "^2.0.1", "kornrunner/secp256k1": "^0.1.2", "php": "^7.2", "simplito/elliptic-php": "^1.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "autoload": {"psr-4": {"IEXBase\\TronAPI\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A PHP API for interacting with Tron (Trx)", "homepage": "https://github.com/iexbase/tron-api", "keywords": ["iexbase", "tron-api", "tron-lib", "tron-php", "tron-rest-api"], "support": {"issues": "https://github.com/iexbase/tron-api/issues", "source": "https://github.com/iexbase/tron-api/tree/v3.1.2"}, "time": "2020-10-15T18:21:16+00:00"}, {"name": "iexbase/web3.php", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/iexbase/web3.php.git", "reference": "f25ed954a7586ead86046dd7e02a333a8098511b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/iexbase/web3.php/zipball/f25ed954a7586ead86046dd7e02a333a8098511b", "reference": "f25ed954a7586ead86046dd7e02a333a8098511b", "shasum": ""}, "require": {"ext-mbstring": "*", "guzzlehttp/guzzle": "^7.0", "kornrunner/keccak": "~1.0", "php": "^7.1", "phpseclib/phpseclib": "~2.0.11"}, "require-dev": {"phpunit/phpunit": "~6.0"}, "type": "library", "autoload": {"psr-4": {"Web3\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Ethereum web3 interface.", "support": {"source": "https://github.com/iexbase/web3.php/tree/2.0.1"}, "time": "2020-10-15T18:16:13+00:00"}, {"name": "jaybizzle/crawler-detect", "version": "v1.2.110", "source": {"type": "git", "url": "https://github.com/JayBizzle/Crawler-Detect.git", "reference": "f9d63a3581428fd8a3858e161d072f0b9debc26f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/JayBizzle/Crawler-Detect/zipball/f9d63a3581428fd8a3858e161d072f0b9debc26f", "reference": "f9d63a3581428fd8a3858e161d072f0b9debc26f", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.5|^6.5|^9.4"}, "type": "library", "autoload": {"psr-4": {"Jaybizzle\\CrawlerDetect\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CrawlerDetect is a PHP class for detecting bots/crawlers/spiders via the user agent", "homepage": "https://github.com/JayBizzle/Crawler-Detect/", "keywords": ["crawler", "crawler detect", "crawler detector", "crawlerdetect", "php crawler detect"], "support": {"issues": "https://github.com/JayBizzle/Crawler-Detect/issues", "source": "https://github.com/JayBizzle/Crawler-Detect/tree/v1.2.110"}, "time": "2021-12-07T18:35:06+00:00"}, {"name": "jean85/pretty-package-versions", "version": "1.6.0", "source": {"type": "git", "url": "https://github.com/Jean85/pretty-package-versions.git", "reference": "1e0104b46f045868f11942aea058cd7186d6c303"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Jean85/pretty-package-versions/zipball/1e0104b46f045868f11942aea058cd7186d6c303", "reference": "1e0104b46f045868f11942aea058cd7186d6c303", "shasum": ""}, "require": {"composer/package-versions-deprecated": "^1.8.0", "php": "^7.0|^8.0"}, "require-dev": {"phpunit/phpunit": "^6.0|^8.5|^9.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Jean85\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A wrapper for ocramius/package-versions to get pretty versions strings", "keywords": ["composer", "package", "release", "versions"], "support": {"issues": "https://github.com/Jean85/pretty-package-versions/issues", "source": "https://github.com/Jean85/pretty-package-versions/tree/1.6.0"}, "time": "2021-02-04T16:20:16+00:00"}, {"name": "jenssegers/agent", "version": "v2.6.4", "source": {"type": "git", "url": "https://github.com/jenssegers/agent.git", "reference": "daa11c43729510b3700bc34d414664966b03bffe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jenssegers/agent/zipball/daa11c43729510b3700bc34d414664966b03bffe", "reference": "daa11c43729510b3700bc34d414664966b03bffe", "shasum": ""}, "require": {"jaybizzle/crawler-detect": "^1.2", "mobiledetect/mobiledetectlib": "^2.7.6", "php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5.0|^6.0|^7.0"}, "suggest": {"illuminate/support": "Required for laravel service providers"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}, "laravel": {"providers": ["Jenssegers\\Agent\\AgentServiceProvider"], "aliases": {"Agent": "Jenssegers\\Agent\\Facades\\Agent"}}}, "autoload": {"psr-4": {"Jenssegers\\Agent\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://jenssegers.com"}], "description": "Desktop/mobile user agent parser with support for <PERSON><PERSON>, based on Mobiledetect", "homepage": "https://github.com/jenssegers/agent", "keywords": ["Agent", "browser", "desktop", "laravel", "mobile", "platform", "user agent", "useragent"], "support": {"issues": "https://github.com/jenssegers/agent/issues", "source": "https://github.com/jenssegers/agent/tree/v2.6.4"}, "funding": [{"url": "https://github.com/jenssegers", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/jenssegers/agent", "type": "tidelift"}], "time": "2020-06-13T08:05:20+00:00"}, {"name": "jenssegers/mongodb", "version": "v3.6.8", "source": {"type": "git", "url": "https://github.com/jenssegers/laravel-mongodb.git", "reference": "07c03110ed208720028f87c83e8c1fafca5d82d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jenssegers/laravel-mongodb/zipball/07c03110ed208720028f87c83e8c1fafca5d82d9", "reference": "07c03110ed208720028f87c83e8c1fafca5d82d9", "shasum": ""}, "require": {"illuminate/container": "^5.8|^6.0", "illuminate/database": "^5.8|^6.0", "illuminate/events": "^5.8|^6.0", "illuminate/support": "^5.8|^6.0", "mongodb/mongodb": "^1.4"}, "require-dev": {"cedx/coveralls": "^11.2", "doctrine/dbal": "^2.5", "mockery/mockery": "^1.0", "orchestra/testbench": "^3.1|^4.0", "phpunit/phpcov": "^6.0", "phpunit/phpunit": "^6.0|^7.0|^8.0"}, "suggest": {"jenssegers/mongodb-sentry": "Add Sentry support to Laravel-MongoDB", "jenssegers/mongodb-session": "Add MongoDB session support to Laravel-MongoDB"}, "type": "library", "extra": {"laravel": {"providers": ["Jenssegers\\Mongodb\\MongodbServiceProvider", "Jenssegers\\Mongodb\\MongodbQueueServiceProvider"]}}, "autoload": {"psr-0": {"Jenssegers\\Mongodb": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://jenssegers.com"}], "description": "A MongoDB based Eloquent model and Query builder for Laravel (Moloquent)", "homepage": "https://github.com/jenssegers/laravel-mongodb", "keywords": ["database", "eloquent", "laravel", "model", "moloquent", "mongo", "mongodb"], "support": {"issues": "https://github.com/jenssegers/laravel-mongodb/issues", "source": "https://github.com/jenssegers/laravel-mongodb/tree/v3.6.8"}, "funding": [{"url": "https://github.com/jenssegers", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/jenssegers/mongodb", "type": "tidelift"}], "time": "2021-02-20T00:11:23+00:00"}, {"name": "kornrunner/keccak", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/kornrunner/php-keccak.git", "reference": "433749d28e117fb97baf9f2631b92b5d9ab3c890"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kornrunner/php-keccak/zipball/433749d28e117fb97baf9f2631b92b5d9ab3c890", "reference": "433749d28e117fb97baf9f2631b92b5d9ab3c890", "shasum": ""}, "require": {"php": ">=7.3", "symfony/polyfill-mbstring": "^1.8"}, "require-dev": {"phpunit/phpunit": "^8.2"}, "type": "library", "autoload": {"psr-4": {"kornrunner\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/kornrunner/php-keccak"}], "description": "Pure PHP implementation of Keccak", "keywords": ["keccak", "sha-3", "sha3-256"], "support": {"issues": "https://github.com/kornrunner/php-keccak/issues", "source": "https://github.com/kornrunner/php-keccak/tree/1.1.0"}, "time": "2020-12-07T15:40:44+00:00"}, {"name": "kornrunner/secp256k1", "version": "0.1.2", "source": {"type": "git", "url": "https://github.com/kornrunner/php-secp256k1.git", "reference": "915f0ef1ec748606a1117b171093266de349b058"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kornrunner/php-secp256k1/zipball/915f0ef1ec748606a1117b171093266de349b058", "reference": "915f0ef1ec748606a1117b171093266de349b058", "shasum": ""}, "require": {"mdanter/ecc": "^0.5", "php": ">=7.1"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^7"}, "type": "library", "autoload": {"psr-4": {"kornrunner\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "boris.mom<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Pure PHP secp256k1", "keywords": ["curve", "ecc", "elliptic", "secp256k1"], "support": {"issues": "https://github.com/kornrunner/php-secp256k1/issues", "source": "https://github.com/kornrunner/php-secp256k1/tree/master"}, "time": "2019-01-16T17:01:51+00:00"}, {"name": "laravel/framework", "version": "v6.20.43", "source": {"type": "git", "url": "https://github.com/laravel/framework.git", "reference": "103df9fafbeeac620305874d54a01288c6f1586b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/framework/zipball/103df9fafbeeac620305874d54a01288c6f1586b", "reference": "103df9fafbeeac620305874d54a01288c6f1586b", "shasum": ""}, "require": {"doctrine/inflector": "^1.4|^2.0", "dragonmantank/cron-expression": "^2.3.1", "egulias/email-validator": "^2.1.10", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "league/commonmark": "^1.3", "league/flysystem": "^1.1", "monolog/monolog": "^1.12|^2.0", "nesbot/carbon": "^2.31", "opis/closure": "^3.6", "php": "^7.2.5|^8.0", "psr/container": "^1.0", "psr/simple-cache": "^1.0", "ramsey/uuid": "^3.7", "swiftmailer/swiftmailer": "^6.0", "symfony/console": "^4.3.4", "symfony/debug": "^4.3.4", "symfony/finder": "^4.3.4", "symfony/http-foundation": "^4.3.4", "symfony/http-kernel": "^4.3.4", "symfony/polyfill-php73": "^1.17", "symfony/process": "^4.3.4", "symfony/routing": "^4.3.4", "symfony/var-dumper": "^4.3.4", "tijsverkoyen/css-to-inline-styles": "^2.2.1", "vlucas/phpdotenv": "^3.3"}, "conflict": {"tightenco/collect": "<5.5.33"}, "replace": {"illuminate/auth": "self.version", "illuminate/broadcasting": "self.version", "illuminate/bus": "self.version", "illuminate/cache": "self.version", "illuminate/config": "self.version", "illuminate/console": "self.version", "illuminate/container": "self.version", "illuminate/contracts": "self.version", "illuminate/cookie": "self.version", "illuminate/database": "self.version", "illuminate/encryption": "self.version", "illuminate/events": "self.version", "illuminate/filesystem": "self.version", "illuminate/hashing": "self.version", "illuminate/http": "self.version", "illuminate/log": "self.version", "illuminate/mail": "self.version", "illuminate/notifications": "self.version", "illuminate/pagination": "self.version", "illuminate/pipeline": "self.version", "illuminate/queue": "self.version", "illuminate/redis": "self.version", "illuminate/routing": "self.version", "illuminate/session": "self.version", "illuminate/support": "self.version", "illuminate/translation": "self.version", "illuminate/validation": "self.version", "illuminate/view": "self.version"}, "require-dev": {"aws/aws-sdk-php": "^3.155", "doctrine/dbal": "^2.6", "filp/whoops": "^2.8", "guzzlehttp/guzzle": "^6.3.1|^7.0.1", "league/flysystem-cached-adapter": "^1.0", "mockery/mockery": "~1.3.3|^1.4.2", "moontoast/math": "^1.1", "orchestra/testbench-core": "^4.8", "pda/pheanstalk": "^4.0", "phpunit/phpunit": "^7.5.15|^8.4|^9.3.3", "predis/predis": "^1.1.1", "symfony/cache": "^4.3.4"}, "suggest": {"aws/aws-sdk-php": "Required to use the SQS queue driver, DynamoDb failed job storage and SES mail driver (^3.155).", "doctrine/dbal": "Required to rename columns and drop SQLite columns (^2.6).", "ext-ftp": "Required to use the Flysystem FTP driver.", "ext-gd": "Required to use Illuminate\\Http\\Testing\\FileFactory::image().", "ext-memcached": "Required to use the memcache cache driver.", "ext-pcntl": "Required to use all features of the queue worker.", "ext-posix": "Required to use all features of the queue worker.", "ext-redis": "Required to use the Redis cache and queue drivers (^4.0|^5.0).", "fakerphp/faker": "Required to use the eloquent factory builder (^1.9.1).", "filp/whoops": "Required for friendly error pages in development (^2.8).", "guzzlehttp/guzzle": "Required to use the Mailgun mail driver and the ping methods on schedules (^6.3.1|^7.0.1).", "laravel/tinker": "Required to use the tinker console command (^2.0).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (^1.0).", "league/flysystem-cached-adapter": "Required to use the Flysystem cache (^1.0).", "league/flysystem-sftp": "Required to use the Flysystem SFTP driver (^1.0).", "moontoast/math": "Required to use ordered UUIDs (^1.1).", "nyholm/psr7": "Required to use PSR-7 bridging features (^1.2).", "pda/pheanstalk": "Required to use the beanstalk queue driver (^4.0).", "predis/predis": "Required to use the predis connector (^1.1.2).", "psr/http-message": "Required to allow Storage::put to accept a StreamInterface (^1.0).", "pusher/pusher-php-server": "Required to use the <PERSON><PERSON><PERSON> broadcast driver (^4.0).", "symfony/cache": "Required to PSR-6 cache bridge (^4.3.4).", "symfony/psr-http-message-bridge": "Required to use PSR-7 bridging features (^1.2).", "wildbit/swiftmailer-postmark": "Required to use Postmark mail driver (^3.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.x-dev"}}, "autoload": {"files": ["src/Illuminate/Foundation/helpers.php", "src/Illuminate/Support/helpers.php"], "psr-4": {"Illuminate\\": "src/Illuminate/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Laravel Framework.", "homepage": "https://laravel.com", "keywords": ["framework", "laravel"], "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2021-12-14T14:39:18+00:00"}, {"name": "laravel/helpers", "version": "v1.4.1", "source": {"type": "git", "url": "https://github.com/laravel/helpers.git", "reference": "febb10d8daaf86123825de2cb87f789a3371f0ac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/helpers/zipball/febb10d8daaf86123825de2cb87f789a3371f0ac", "reference": "febb10d8daaf86123825de2cb87f789a3371f0ac", "shasum": ""}, "require": {"illuminate/support": "~5.8.0|^6.0|^7.0|^8.0", "php": "^7.1.3|^8.0"}, "require-dev": {"phpunit/phpunit": "^7.0|^8.0|^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"files": ["src/helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Provides backwards compatibility for helpers in the latest Laravel release.", "keywords": ["helpers", "laravel"], "support": {"source": "https://github.com/laravel/helpers/tree/v1.4.1"}, "time": "2021-02-16T15:27:11+00:00"}, {"name": "laravel/tinker", "version": "v1.0.10", "source": {"type": "git", "url": "https://github.com/laravel/tinker.git", "reference": "ad571aacbac1539c30d480908f9d0c9614eaf1a7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/tinker/zipball/ad571aacbac1539c30d480908f9d0c9614eaf1a7", "reference": "ad571aacbac1539c30d480908f9d0c9614eaf1a7", "shasum": ""}, "require": {"illuminate/console": "~5.1|^6.0", "illuminate/contracts": "~5.1|^6.0", "illuminate/support": "~5.1|^6.0", "php": ">=5.5.9", "psy/psysh": "0.7.*|0.8.*|0.9.*", "symfony/var-dumper": "~3.0|~4.0"}, "require-dev": {"phpunit/phpunit": "~4.0|~5.0"}, "suggest": {"illuminate/database": "The Illuminate Database package (~5.1)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}, "laravel": {"providers": ["Laravel\\Tinker\\TinkerServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Tinker\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful REPL for the Laravel framework.", "keywords": ["REPL", "Tinker", "laravel", "psysh"], "support": {"issues": "https://github.com/laravel/tinker/issues", "source": "https://github.com/laravel/tinker/tree/v1.0.10"}, "time": "2019-08-07T15:10:45+00:00"}, {"name": "lastguest/murmurhash", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/lastguest/murmurhash-php.git", "reference": "4fb7516f67e695e5d7fa129d1bbb925ec0ebe408"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lastguest/murmurhash-php/zipball/4fb7516f67e695e5d7fa129d1bbb925ec0ebe408", "reference": "4fb7516f67e695e5d7fa129d1bbb925ec0ebe408", "shasum": ""}, "require": {"php": "^7"}, "require-dev": {"phpstan/phpstan": "^0.6.3", "phpunit/phpunit": "^5"}, "type": "library", "autoload": {"psr-4": {"lastguest\\": "src/lastguest/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/lastguest/murmurhash-php"}], "description": "MurmurHash3 Hash", "homepage": "https://github.com/lastguest/murmurhash-php", "keywords": ["hash", "hashing", "murmur"], "support": {"issues": "https://github.com/lastguest/murmurhash-php/issues", "source": "https://github.com/lastguest/murmurhash-php/tree/master"}, "time": "2017-10-10T15:16:12+00:00"}, {"name": "lcobucci/jwt", "version": "3.3.3", "source": {"type": "git", "url": "https://github.com/lcobucci/jwt.git", "reference": "c1123697f6a2ec29162b82f170dd4a491f524773"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lcobucci/jwt/zipball/c1123697f6a2ec29162b82f170dd4a491f524773", "reference": "c1123697f6a2ec29162b82f170dd4a491f524773", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-openssl": "*", "php": "^5.6 || ^7.0"}, "require-dev": {"mikey179/vfsstream": "~1.5", "phpmd/phpmd": "~2.2", "phpunit/php-invoker": "~1.1", "phpunit/phpunit": "^5.7 || ^7.3", "squizlabs/php_codesniffer": "~2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Lcobucci\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to work with JSON Web Token and JSON Web Signature", "keywords": ["JWS", "jwt"], "support": {"issues": "https://github.com/lcobucci/jwt/issues", "source": "https://github.com/lcobucci/jwt/tree/3.3.3"}, "funding": [{"url": "https://github.com/lcobucci", "type": "github"}, {"url": "https://www.patreon.com/lcobucci", "type": "patreon"}], "time": "2020-08-20T13:22:28+00:00"}, {"name": "league/commonmark", "version": "1.6.6", "source": {"type": "git", "url": "https://github.com/thephpleague/commonmark.git", "reference": "c4228d11e30d7493c6836d20872f9582d8ba6dcf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/commonmark/zipball/c4228d11e30d7493c6836d20872f9582d8ba6dcf", "reference": "c4228d11e30d7493c6836d20872f9582d8ba6dcf", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^7.1 || ^8.0"}, "conflict": {"scrutinizer/ocular": "1.7.*"}, "require-dev": {"cebe/markdown": "~1.0", "commonmark/commonmark.js": "0.29.2", "erusev/parsedown": "~1.0", "ext-json": "*", "github/gfm": "0.29.0", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "^1.4", "phpstan/phpstan": "^0.12.90", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.2", "scrutinizer/ocular": "^1.5", "symfony/finder": "^4.2"}, "bin": ["bin/commonmark"], "type": "library", "autoload": {"psr-4": {"League\\CommonMark\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}], "description": "Highly-extensible PHP Markdown parser which fully supports the CommonMark spec and Github-Flavored Markdown (GFM)", "homepage": "https://commonmark.thephpleague.com", "keywords": ["commonmark", "flavored", "gfm", "github", "github-flavored", "markdown", "md", "parser"], "support": {"docs": "https://commonmark.thephpleague.com/", "issues": "https://github.com/thephpleague/commonmark/issues", "rss": "https://github.com/thephpleague/commonmark/releases.atom", "source": "https://github.com/thephpleague/commonmark"}, "funding": [{"url": "https://enjoy.gitstore.app/repositories/thephpleague/commonmark", "type": "custom"}, {"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}, {"url": "https://www.patreon.com/colinodell", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/league/commonmark", "type": "tidelift"}], "time": "2021-07-17T17:13:23+00:00"}, {"name": "league/flysystem", "version": "1.1.9", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "094defdb4a7001845300334e7c1ee2335925ef99"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/094defdb4a7001845300334e7c1ee2335925ef99", "reference": "094defdb4a7001845300334e7c1ee2335925ef99", "shasum": ""}, "require": {"ext-fileinfo": "*", "league/mime-type-detection": "^1.3", "php": "^7.2.5 || ^8.0"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/prophecy": "^1.11.1", "phpunit/phpunit": "^8.5.8"}, "suggest": {"ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.1.9"}, "funding": [{"url": "https://offset.earth/frankdejonge", "type": "other"}], "time": "2021-12-09T09:40:50+00:00"}, {"name": "league/fractal", "version": "0.17.0", "source": {"type": "git", "url": "https://github.com/thephpleague/fractal.git", "reference": "a0b350824f22fc2fdde2500ce9d6851a3f275b0e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/fractal/zipball/a0b350824f22fc2fdde2500ce9d6851a3f275b0e", "reference": "a0b350824f22fc2fdde2500ce9d6851a3f275b0e", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"doctrine/orm": "^2.5", "illuminate/contracts": "~5.0", "mockery/mockery": "~0.9", "pagerfanta/pagerfanta": "~1.0.0", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~1.5", "zendframework/zend-paginator": "~2.3"}, "suggest": {"illuminate/pagination": "The Illuminate Pagination component.", "pagerfanta/pagerfanta": "Pagerfant<PERSON>", "zendframework/zend-paginator": "Zend Framework Paginator"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.13-dev"}}, "autoload": {"psr-4": {"League\\Fractal\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://philsturgeon.uk/", "role": "Developer"}], "description": "Handle the output of complex data structures ready for API output.", "homepage": "http://fractal.thephpleague.com/", "keywords": ["api", "json", "league", "rest"], "support": {"issues": "https://github.com/thephpleague/fractal/issues", "source": "https://github.com/thephpleague/fractal/tree/master"}, "time": "2017-06-12T11:04:56+00:00"}, {"name": "league/mime-type-detection", "version": "1.9.0", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "aa70e813a6ad3d1558fc927863d47309b4c23e69"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/aa70e813a6ad3d1558fc927863d47309b4c23e69", "reference": "aa70e813a6ad3d1558fc927863d47309b4c23e69", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3"}, "type": "library", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.9.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2021-11-21T11:48:40+00:00"}, {"name": "mdanter/ecc", "version": "v0.5.2", "source": {"type": "git", "url": "https://github.com/phpecc/phpecc.git", "reference": "b95f25cc1bacc83a9f0ccd375900b7cfd343029e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpecc/phpecc/zipball/b95f25cc1bacc83a9f0ccd375900b7cfd343029e", "reference": "b95f25cc1bacc83a9f0ccd375900b7cfd343029e", "shasum": ""}, "require": {"ext-gmp": "*", "fgrosse/phpasn1": "^2.0", "php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0", "squizlabs/php_codesniffer": "^2.0", "symfony/yaml": "^2.6|^3.0"}, "type": "library", "autoload": {"psr-4": {"Mdanter\\Ecc\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "http://matejdanter.com/", "role": "Author"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://aztech.io", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Maintainer"}], "description": "PHP Elliptic Curve Cryptography library", "homepage": "https://github.com/phpecc/phpecc", "keywords": ["<PERSON><PERSON><PERSON>", "ECDSA", "<PERSON><PERSON>", "curve", "ecdh", "elliptic", "nistp192", "nistp224", "nistp256", "nistp384", "nistp521", "phpecc", "secp256k1", "secp256r1"], "support": {"issues": "https://github.com/phpecc/phpecc/issues", "source": "https://github.com/phpecc/phpecc/tree/master"}, "time": "2018-12-03T18:17:01+00:00"}, {"name": "medz/gb-t-2260", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/medz/gb-t-2260.git", "reference": "002589f61bbfef5c62be69ed8e952a8360a506c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/medz/gb-t-2260/zipball/002589f61bbfef5c62be69ed8e952a8360a506c7", "reference": "002589f61bbfef5c62be69ed8e952a8360a506c7", "shasum": ""}, "require": {"php": ">=7.1.3"}, "require-dev": {"guzzlehttp/guzzle": "^6.5"}, "type": "library", "autoload": {"psr-4": {"Medz\\GBT2260\\": "php"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Seven Du", "email": "<EMAIL>", "homepage": "https://github.com/medz", "role": "The tool source developer"}], "description": "中华人民共和国国家标准 GB/T 2260 行政区划代码", "homepage": "https://github.com/medz/gb-t-2260", "keywords": ["2260", "China", "GB", "GB-T", "GB-T 2260"], "support": {"docs": "https://github.com/medz/gb-t-2260#readme", "issues": "https://github.com/medz/gb-t-2260/issues", "source": "https://github.com/medz/gb-t-2260.git"}, "time": "2020-04-13T09:14:56+00:00"}, {"name": "medz/id-card-of-china", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/medz/id-card-of-china.git", "reference": "107ffb1f81aa563c93bb7e6d2eae7cc0e78bb7c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/medz/id-card-of-china/zipball/107ffb1f81aa563c93bb7e6d2eae7cc0e78bb7c1", "reference": "107ffb1f81aa563c93bb7e6d2eae7cc0e78bb7c1", "shasum": ""}, "require": {"medz/gb-t-2260": "^2.0", "php": ">=7.1.3"}, "require-dev": {"phpunit/phpunit": "^6.5"}, "type": "library", "autoload": {"psr-4": {"Medz\\IdentityCard\\China\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Seven Du", "email": "<EMAIL>"}], "description": "中华人民共和国身份证（The identity card of the people's Republic of China）", "keywords": ["China", "card", "id", "identity"], "support": {"issues": "https://github.com/medz/id-card-of-china/issues", "source": "https://github.com/medz/id-card-of-china/tree/1.1.0"}, "time": "2019-12-15T06:13:42+00:00"}, {"name": "mobiledetect/mobiledetectlib", "version": "2.8.37", "source": {"type": "git", "url": "https://github.com/serbanghita/Mobile-Detect.git", "reference": "9841e3c46f5bd0739b53aed8ac677fa712943df7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/serbanghita/Mobile-Detect/zipball/9841e3c46f5bd0739b53aed8ac677fa712943df7", "reference": "9841e3c46f5bd0739b53aed8ac677fa712943df7", "shasum": ""}, "require": {"php": ">=5.0.0"}, "require-dev": {"phpunit/phpunit": "~4.8.35||~5.7"}, "type": "library", "autoload": {"classmap": ["Mobile_Detect.php"], "psr-0": {"Detection": "namespaced/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "serban<PERSON><PERSON>@gmail.com", "homepage": "http://mobiledetect.net", "role": "Developer"}], "description": "Mobile_Detect is a lightweight PHP class for detecting mobile devices. It uses the User-Agent string combined with specific HTTP headers to detect the mobile environment.", "homepage": "https://github.com/serbanghita/Mobile-Detect", "keywords": ["detect mobile devices", "mobile", "mobile detect", "mobile detector", "php mobile detect"], "support": {"issues": "https://github.com/serbanghita/Mobile-Detect/issues", "source": "https://github.com/serbanghita/Mobile-Detect/tree/2.8.37"}, "funding": [{"url": "https://github.com/serbanghita", "type": "github"}], "time": "2021-02-19T21:22:57+00:00"}, {"name": "mongodb/mongodb", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/mongodb/mongo-php-library.git", "reference": "953dbc19443aa9314c44b7217a16873347e6840d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mongodb/mongo-php-library/zipball/953dbc19443aa9314c44b7217a16873347e6840d", "reference": "953dbc19443aa9314c44b7217a16873347e6840d", "shasum": ""}, "require": {"ext-hash": "*", "ext-json": "*", "ext-mongodb": "^1.8.1", "jean85/pretty-package-versions": "^1.2", "php": "^7.0 || ^8.0", "symfony/polyfill-php80": "^1.19"}, "require-dev": {"squizlabs/php_codesniffer": "^3.5, <3.5.5", "symfony/phpunit-bridge": "5.x-dev"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8.x-dev"}}, "autoload": {"psr-4": {"MongoDB\\": "src/"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "MongoDB driver library", "homepage": "https://jira.mongodb.org/browse/PHPLIB", "keywords": ["database", "driver", "mongodb", "persistence"], "support": {"issues": "https://github.com/mongodb/mongo-php-library/issues", "source": "https://github.com/mongodb/mongo-php-library/tree/1.8.0"}, "time": "2020-11-25T12:26:02+00:00"}, {"name": "monolog/monolog", "version": "2.3.5", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "fd4380d6fc37626e2f799f29d91195040137eba9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/fd4380d6fc37626e2f799f29d91195040137eba9", "reference": "fd4380d6fc37626e2f799f29d91195040137eba9", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "1.0.0 || 2.0.0 || 3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7", "graylog2/gelf-php": "^1.4.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.3", "phpspec/prophecy": "^1.6.1", "phpstan/phpstan": "^0.12.91", "phpunit/phpunit": "^8.5", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3", "ruflin/elastica": ">=0.90@dev", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.3.5"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2021-10-01T21:08:31+00:00"}, {"name": "namshi/jose", "version": "7.2.3", "source": {"type": "git", "url": "https://github.com/namshi/jose.git", "reference": "89a24d7eb3040e285dd5925fcad992378b82bcff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/namshi/jose/zipball/89a24d7eb3040e285dd5925fcad992378b82bcff", "reference": "89a24d7eb3040e285dd5925fcad992378b82bcff", "shasum": ""}, "require": {"ext-date": "*", "ext-hash": "*", "ext-json": "*", "ext-pcre": "*", "ext-spl": "*", "php": ">=5.5", "symfony/polyfill-php56": "^1.0"}, "require-dev": {"phpseclib/phpseclib": "^2.0", "phpunit/phpunit": "^4.5|^5.0", "satooshi/php-coveralls": "^1.0"}, "suggest": {"ext-openssl": "Allows to use OpenSSL as crypto engine.", "phpseclib/phpseclib": "Allows to use Phpseclib as crypto engine, use version ^2.0."}, "type": "library", "autoload": {"psr-4": {"Namshi\\JOSE\\": "src/Namshi/JOSE/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON> (cirpo)", "email": "<EMAIL>"}], "description": "JSON Object Signing and Encryption library for PHP.", "keywords": ["JSON Web Signature", "JSON Web Token", "JWS", "json", "jwt", "token"], "support": {"issues": "https://github.com/namshi/jose/issues", "source": "https://github.com/namshi/jose/tree/master"}, "time": "2016-12-05T07:27:31+00:00"}, {"name": "nesbot/carbon", "version": "2.55.2", "source": {"type": "git", "url": "https://github.com/briannesbitt/Carbon.git", "reference": "8c2a18ce3e67c34efc1b29f64fe61304368259a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/8c2a18ce3e67c34efc1b29f64fe61304368259a2", "reference": "8c2a18ce3e67c34efc1b29f64fe61304368259a2", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.1.8 || ^8.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "require-dev": {"doctrine/dbal": "^2.0 || ^3.0", "doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.54", "phpunit/phpunit": "^7.5.20 || ^8.5.14", "squizlabs/php_codesniffer": "^3.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"branch-alias": {"dev-3.x": "3.x-dev", "dev-master": "2.x-dev"}, "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2021-12-03T14:59:52+00:00"}, {"name": "nikic/php-parser", "version": "v4.13.2", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "210577fe3cf7badcc5814d99455df46564f3c077"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/210577fe3cf7badcc5814d99455df46564f3c077", "reference": "210577fe3cf7badcc5814d99455df46564f3c077", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.13.2"}, "time": "2021-11-30T19:35:32+00:00"}, {"name": "opis/closure", "version": "3.6.2", "source": {"type": "git", "url": "https://github.com/opis/closure.git", "reference": "06e2ebd25f2869e54a306dda991f7db58066f7f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opis/closure/zipball/06e2ebd25f2869e54a306dda991f7db58066f7f6", "reference": "06e2ebd25f2869e54a306dda991f7db58066f7f6", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0"}, "require-dev": {"jeremeamia/superclosure": "^2.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.6.x-dev"}}, "autoload": {"psr-4": {"Opis\\Closure\\": "src/"}, "files": ["functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Sorin Sarca", "email": "<EMAIL>"}], "description": "A library that can be used to serialize closures (anonymous functions) and arbitrary objects.", "homepage": "https://opis.io/closure", "keywords": ["anonymous functions", "closure", "function", "serializable", "serialization", "serialize"], "support": {"issues": "https://github.com/opis/closure/issues", "source": "https://github.com/opis/closure/tree/3.6.2"}, "time": "2021-04-09T13:42:10+00:00"}, {"name": "overtrue/easy-sms", "version": "1.3.2", "source": {"type": "git", "url": "https://github.com/overtrue/easy-sms.git", "reference": "daa0b4308ec0e3c112888c288d14d473be6aabee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/overtrue/easy-sms/zipball/daa0b4308ec0e3c112888c288d14d473be6aabee", "reference": "daa0b4308ec0e3c112888c288d14d473be6aabee", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/guzzle": "^6.2 || ^7.0", "php": ">=5.6"}, "require-dev": {"mockery/mockery": "1.3.1", "phpunit/phpunit": "^5.7 || ^7.5"}, "type": "library", "autoload": {"psr-4": {"Overtrue\\EasySms\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "description": "The easiest way to send short message.", "support": {"issues": "https://github.com/overtrue/easy-sms/issues", "source": "https://github.com/overtrue/easy-sms/tree/1.3.2"}, "time": "2021-01-22T06:52:59+00:00"}, {"name": "paragonie/constant_time_encoding", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "f34c2b11eb9d2c9318e13540a1dbc2a3afbd939c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/f34c2b11eb9d2c9318e13540a1dbc2a3afbd939c", "reference": "f34c2b11eb9d2c9318e13540a1dbc2a3afbd939c", "shasum": ""}, "require": {"php": "^7|^8"}, "require-dev": {"phpunit/phpunit": "^6|^7|^8|^9", "vimeo/psalm": "^1|^2|^3|^4"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/constant_time_encoding/issues", "source": "https://github.com/paragonie/constant_time_encoding"}, "time": "2020-12-06T15:14:20+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "php-parallel-lint/php-console-color", "version": "v0.3", "source": {"type": "git", "url": "https://github.com/php-parallel-lint/PHP-Console-Color.git", "reference": "b6af326b2088f1ad3b264696c9fd590ec395b49e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-parallel-lint/PHP-Console-Color/zipball/b6af326b2088f1ad3b264696c9fd590ec395b49e", "reference": "b6af326b2088f1ad3b264696c9fd590ec395b49e", "shasum": ""}, "require": {"php": ">=5.4.0"}, "replace": {"jakub-onderka/php-console-color": "*"}, "require-dev": {"php-parallel-lint/php-code-style": "1.0", "php-parallel-lint/php-parallel-lint": "1.0", "php-parallel-lint/php-var-dump-check": "0.*", "phpunit/phpunit": "~4.3", "squizlabs/php_codesniffer": "1.*"}, "type": "library", "autoload": {"psr-4": {"JakubOnderka\\PhpConsoleColor\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/php-parallel-lint/PHP-Console-Color/issues", "source": "https://github.com/php-parallel-lint/PHP-Console-Color/tree/master"}, "time": "2020-05-14T05:47:14+00:00"}, {"name": "php-parallel-lint/php-console-highlighter", "version": "v0.5", "source": {"type": "git", "url": "https://github.com/php-parallel-lint/PHP-Console-Highlighter.git", "reference": "21bf002f077b177f056d8cb455c5ed573adfdbb8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-parallel-lint/PHP-Console-Highlighter/zipball/21bf002f077b177f056d8cb455c5ed573adfdbb8", "reference": "21bf002f077b177f056d8cb455c5ed573adfdbb8", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.4.0", "php-parallel-lint/php-console-color": "~0.2"}, "replace": {"jakub-onderka/php-console-highlighter": "*"}, "require-dev": {"php-parallel-lint/php-code-style": "~1.0", "php-parallel-lint/php-parallel-lint": "~1.0", "php-parallel-lint/php-var-dump-check": "~0.1", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~1.5"}, "type": "library", "autoload": {"psr-4": {"JakubOnderka\\PhpConsoleHighlighter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.acci.cz/"}], "description": "Highlight PHP code in terminal", "support": {"issues": "https://github.com/php-parallel-lint/PHP-Console-Highlighter/issues", "source": "https://github.com/php-parallel-lint/PHP-Console-Highlighter/tree/master"}, "time": "2020-05-13T07:37:49+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "4.3.4", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "da3fd972d6bafd628114f7e7e036f45944b62e9c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/da3fd972d6bafd628114f7e7e036f45944b62e9c", "reference": "da3fd972d6bafd628114f7e7e036f45944b62e9c", "shasum": ""}, "require": {"php": "^7.0", "phpdocumentor/reflection-common": "^1.0.0 || ^2.0.0", "phpdocumentor/type-resolver": "~0.4 || ^1.0.0", "webmozart/assert": "^1.0"}, "require-dev": {"doctrine/instantiator": "^1.0.5", "mockery/mockery": "^1.0", "phpdocumentor/type-resolver": "0.4.*", "phpunit/phpunit": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/release/4.x"}, "time": "2019-12-28T18:55:12+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "a12f7e301eb7258bb68acd89d4aefa05c2906cae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/a12f7e301eb7258bb68acd89d4aefa05c2906cae", "reference": "a12f7e301eb7258bb68acd89d4aefa05c2906cae", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "*", "psalm/phar": "^4.8"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.5.1"}, "time": "2021-10-02T14:08:47+00:00"}, {"name": "phpoption/phpoption", "version": "1.8.1", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "eab7a0df01fe2344d172bff4cd6dbd3f8b84ad15"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/eab7a0df01fe2344d172bff4cd6dbd3f8b84ad15", "reference": "eab7a0df01fe2344d172bff4cd6dbd3f8b84ad15", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "phpunit/phpunit": "^6.5.14 || ^7.5.20 || ^8.5.19 || ^9.5.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.8.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "time": "2021-12-04T23:24:31+00:00"}, {"name": "phpseclib/phpseclib", "version": "2.0.35", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "4e16cf3f5f927a7d3f5317820af795c0366c0420"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/4e16cf3f5f927a7d3f5317820af795c0366c0420", "reference": "4e16cf3f5f927a7d3f5317820af795c0366c0420", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "^4.8.35|^5.7|^6.0|^9.4", "squizlabs/php_codesniffer": "~2.0"}, "suggest": {"ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.35"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2021-11-28T23:30:39+00:00"}, {"name": "pleonasm/merkle-tree", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/pleonasm/merkle-tree.git", "reference": "9ddc9d0a0e396750fada378f3aa90f6c02dd56a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pleonasm/merkle-tree/zipball/9ddc9d0a0e396750fada378f3aa90f6c02dd56a1", "reference": "9ddc9d0a0e396750fada378f3aa90f6c02dd56a1", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"ext-xdebug": ">=2.2.0", "phpunit/php-invoker": ">=1.0.0,<1.2.0", "phpunit/phpunit": "3.7.19", "satooshi/php-coveralls": "*@dev", "squizlabs/php_codesniffer": "*"}, "type": "library", "autoload": {"psr-0": {"Pleo": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "An implementation of a Merkle Tree in PHP", "support": {"issues": "https://github.com/pleonasm/merkle-tree/issues", "source": "https://github.com/pleonasm/merkle-tree/tree/master"}, "time": "2013-05-22T20:46:20+00:00"}, {"name": "pragmarx/google2fa", "version": "8.0.0", "source": {"type": "git", "url": "https://github.com/antonioribeiro/google2fa.git", "reference": "26c4c5cf30a2844ba121760fd7301f8ad240100b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/antonioribeiro/google2fa/zipball/26c4c5cf30a2844ba121760fd7301f8ad240100b", "reference": "26c4c5cf30a2844ba121760fd7301f8ad240100b", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1.0|^2.0", "php": "^7.1|^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.18", "phpunit/phpunit": "^7.5.15|^8.5|^9.0"}, "type": "library", "autoload": {"psr-4": {"PragmaRX\\Google2FA\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Creator & Designer"}], "description": "A One Time Password Authentication package, compatible with Google Authenticator.", "keywords": ["2fa", "Authentication", "Two Factor Authentication", "google2fa"], "support": {"issues": "https://github.com/antonioribeiro/google2fa/issues", "source": "https://github.com/antonioribeiro/google2fa/tree/8.0.0"}, "time": "2020-04-05T10:47:18+00:00"}, {"name": "pragmarx/google2fa-laravel", "version": "v1.4.1", "source": {"type": "git", "url": "https://github.com/antonioribeiro/google2fa-laravel.git", "reference": "f9014fd7ea36a1f7fffa233109cf59b209469647"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/antonioribeiro/google2fa-laravel/zipball/f9014fd7ea36a1f7fffa233109cf59b209469647", "reference": "f9014fd7ea36a1f7fffa233109cf59b209469647", "shasum": ""}, "require": {"laravel/framework": ">=5.4.36|^8.0", "php": ">=7.0", "pragmarx/google2fa-qrcode": "^1.0"}, "require-dev": {"orchestra/testbench": "3.4.*|3.5.*|3.6.*|3.7.*|4.*|5.*|6.*", "phpunit/phpunit": "~5|~6|~7|~8"}, "suggest": {"bacon/bacon-qr-code": "Required to generate inline QR Codes.", "pragmarx/recovery": "Generate recovery codes."}, "type": "library", "extra": {"component": "package", "frameworks": ["<PERSON><PERSON>"], "branch-alias": {"dev-master": "0.2-dev"}, "laravel": {"providers": ["PragmaRX\\Google2FALaravel\\ServiceProvider"], "aliases": {"Google2FA": "PragmaRX\\Google2FALaravel\\Facade"}}}, "autoload": {"psr-4": {"PragmaRX\\Google2FALaravel\\": "src/", "PragmaRX\\Google2FALaravel\\Tests\\": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Creator & Designer"}], "description": "A One Time Password Authentication package, compatible with Google Authenticator.", "keywords": ["Authentication", "Two Factor Authentication", "google2fa", "laravel"], "support": {"issues": "https://github.com/antonioribeiro/google2fa-laravel/issues", "source": "https://github.com/antonioribeiro/google2fa-laravel/tree/v1.4.1"}, "time": "2020-09-20T21:01:48+00:00"}, {"name": "pragmarx/google2fa-qrcode", "version": "v1.0.3", "source": {"type": "git", "url": "https://github.com/antonioribeiro/google2fa-qrcode.git", "reference": "fd5ff0531a48b193a659309cc5fb882c14dbd03f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/antonioribeiro/google2fa-qrcode/zipball/fd5ff0531a48b193a659309cc5fb882c14dbd03f", "reference": "fd5ff0531a48b193a659309cc5fb882c14dbd03f", "shasum": ""}, "require": {"bacon/bacon-qr-code": "~1.0|~2.0", "php": ">=5.4", "pragmarx/google2fa": ">=4.0"}, "require-dev": {"khanamiryan/qrcode-detector-decoder": "^1.0", "phpunit/phpunit": "~4|~5|~6|~7"}, "type": "library", "extra": {"component": "package", "branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"PragmaRX\\Google2FAQRCode\\": "src/", "PragmaRX\\Google2FAQRCode\\Tests\\": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Creator & Designer"}], "description": "QR Code package for Google2FA", "keywords": ["2fa", "Authentication", "Two Factor Authentication", "google2fa", "qr code", "qrcode"], "support": {"issues": "https://github.com/antonioribeiro/google2fa-qrcode/issues", "source": "https://github.com/antonioribeiro/google2fa-qrcode/tree/master"}, "time": "2019-03-20T16:42:58+00:00"}, {"name": "predis/predis", "version": "v1.1.9", "source": {"type": "git", "url": "https://github.com/predis/predis.git", "reference": "c50c3393bb9f47fa012d0cdfb727a266b0818259"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/predis/predis/zipball/c50c3393bb9f47fa012d0cdfb727a266b0818259", "reference": "c50c3393bb9f47fa012d0cdfb727a266b0818259", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "suggest": {"ext-curl": "Allows access to Webdis when paired with phpiredis", "ext-phpiredis": "Allows faster serialization and deserialization of the Redis protocol"}, "type": "library", "autoload": {"psr-4": {"Predis\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://clorophilla.net", "role": "Creator & Maintainer"}, {"name": "<PERSON>", "homepage": "https://till.im", "role": "Maintainer"}], "description": "Flexible and feature-complete Redis client for PHP and HHVM", "homepage": "http://github.com/predis/predis", "keywords": ["nosql", "predis", "redis"], "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v1.1.9"}, "funding": [{"url": "https://github.com/sponsors/tillkruss", "type": "github"}], "time": "2021-10-05T19:02:38+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "8622567409010282b7aeebe4bb841fe98b58dcaf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/8622567409010282b7aeebe4bb841fe98b58dcaf", "reference": "8622567409010282b7aeebe4bb841fe98b58dcaf", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.1"}, "time": "2021-03-05T17:36:06+00:00"}, {"name": "psr/http-client", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client/tree/master"}, "time": "2020-06-29T06:28:15+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/master"}, "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "time": "2017-10-23T01:57:42+00:00"}, {"name": "psy/psysh", "version": "v0.9.12", "source": {"type": "git", "url": "https://github.com/bobthecow/psysh.git", "reference": "90da7f37568aee36b116a030c5f99c915267edd4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/90da7f37568aee36b116a030c5f99c915267edd4", "reference": "90da7f37568aee36b116a030c5f99c915267edd4", "shasum": ""}, "require": {"dnoegel/php-xdg-base-dir": "0.1.*", "ext-json": "*", "ext-tokenizer": "*", "jakub-onderka/php-console-highlighter": "0.3.*|0.4.*", "nikic/php-parser": "~1.3|~2.0|~3.0|~4.0", "php": ">=5.4.0", "symfony/console": "~2.3.10|^2.4.2|~3.0|~4.0|~5.0", "symfony/var-dumper": "~2.7|~3.0|~4.0|~5.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2", "hoa/console": "~2.15|~3.16", "phpunit/phpunit": "~4.8.35|~5.0|~6.0|~7.0"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well.", "ext-readline": "Enables support for arrow-key history navigation, and showing and manipulating command history.", "hoa/console": "A pure PHP readline implementation. You'll want this if your PHP install doesn't already support readline or libedit."}, "bin": ["bin/psysh"], "type": "library", "extra": {"branch-alias": {"dev-develop": "0.9.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.9.12"}, "time": "2019-12-06T14:19:43+00:00"}, {"name": "qiniu/php-sdk", "version": "v7.4.1", "source": {"type": "git", "url": "https://github.com/qiniu/php-sdk.git", "reference": "10c7ead8357743b4b987a335c14964fb07700d57"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/qiniu/php-sdk/zipball/10c7ead8357743b4b987a335c14964fb07700d57", "reference": "10c7ead8357743b4b987a335c14964fb07700d57", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"paragonie/random_compat": ">=2", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~3.6"}, "type": "library", "autoload": {"psr-4": {"Qiniu\\": "src/<PERSON>iu"}, "files": ["src/Qiniu/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.qiniu.com"}], "description": "Qiniu Resource (Cloud) Storage SDK for PHP", "homepage": "http://developer.qiniu.com/", "keywords": ["cloud", "qiniu", "sdk", "storage"], "support": {"issues": "https://github.com/qiniu/php-sdk/issues", "source": "https://github.com/qiniu/php-sdk/tree/v7.4.1"}, "time": "2021-09-24T09:39:16+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/uuid", "version": "3.9.6", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "ffa80ab953edd85d5b6c004f96181a538aad35a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/ffa80ab953edd85d5b6c004f96181a538aad35a3", "reference": "ffa80ab953edd85d5b6c004f96181a538aad35a3", "shasum": ""}, "require": {"ext-json": "*", "paragonie/random_compat": "^1 | ^2 | ^9.99.99", "php": "^5.4 | ^7.0 | ^8.0", "symfony/polyfill-ctype": "^1.8"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"codeception/aspect-mock": "^1 | ^2", "doctrine/annotations": "^1.2", "goaop/framework": "1.0.0-alpha.2 | ^1 | >=2.1.0 <=2.3.2", "mockery/mockery": "^0.9.11 | ^1", "moontoast/math": "^1.1", "nikic/php-parser": "<=4.5.0", "paragonie/random-lib": "^2", "php-mock/php-mock-phpunit": "^0.3 | ^1.1 | ^2.6", "php-parallel-lint/php-parallel-lint": "^1.3", "phpunit/phpunit": ">=4.8.36 <9.0.0 | >=9.3.0", "squizlabs/php_codesniffer": "^3.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-ctype": "Provides support for PHP Ctype functions", "ext-libsodium": "Provides the PECL libsodium extension for use with the SodiumRandomGenerator", "ext-openssl": "Provides the OpenSSL extension for use with the OpenSslGenerator", "ext-uuid": "Provides the PECL UUID extension for use with the PeclUuidTimeGenerator and PeclUuidRandomGenerator", "moontoast/math": "Provides support for converting UUID to 128-bit integer (in string form).", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-console": "A console application for generating UUIDs with ramsey/uuid", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Ramsey\\Uuid\\": "src/"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Formerly rhumsaa/uuid. A PHP 5.4+ library for generating RFC 4122 version 1, 3, 4, and 5 universally unique identifiers (UUID).", "homepage": "https://github.com/ramsey/uuid", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "rss": "https://github.com/ramsey/uuid/releases.atom", "source": "https://github.com/ramsey/uuid", "wiki": "https://github.com/ramsey/uuid/wiki"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/uuid", "type": "tidelift"}], "time": "2021-09-25T23:07:42+00:00"}, {"name": "simplito/bigint-wrapper-php", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/simplito/bigint-wrapper-php.git", "reference": "cf21ec76d33f103add487b3eadbd9f5033a25930"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplito/bigint-wrapper-php/zipball/cf21ec76d33f103add487b3eadbd9f5033a25930", "reference": "cf21ec76d33f103add487b3eadbd9f5033a25930", "shasum": ""}, "type": "library", "autoload": {"psr-4": {"BI\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Simplito Team", "email": "s.s<PERSON><PERSON><PERSON><PERSON>@simplito.com", "homepage": "https://simplito.com"}], "description": "Common interface for php_gmp and php_bcmath modules", "support": {"issues": "https://github.com/simplito/bigint-wrapper-php/issues", "source": "https://github.com/simplito/bigint-wrapper-php/tree/1.0.0"}, "time": "2018-02-27T12:38:08+00:00"}, {"name": "simplito/bn-php", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/simplito/bn-php.git", "reference": "e852fcd27e4acbc32459606d7606e45a85e42465"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplito/bn-php/zipball/e852fcd27e4acbc32459606d7606e45a85e42465", "reference": "e852fcd27e4acbc32459606d7606e45a85e42465", "shasum": ""}, "require": {"simplito/bigint-wrapper-php": "~1.0.0"}, "require-dev": {"phpunit/phpunit": "*"}, "type": "library", "autoload": {"psr-4": {"BN\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Simplito Team", "email": "s.s<PERSON><PERSON><PERSON><PERSON>@simplito.com", "homepage": "https://simplito.com"}], "description": "Big number implementation compatible with bn.js", "support": {"issues": "https://github.com/simplito/bn-php/issues", "source": "https://github.com/simplito/bn-php/tree/1.1.2"}, "time": "2018-04-12T11:07:43+00:00"}, {"name": "simplito/elliptic-php", "version": "1.0.9", "source": {"type": "git", "url": "https://github.com/simplito/elliptic-php.git", "reference": "18a72b837b845bf9a2ad2c0050eaf864a22b7550"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplito/elliptic-php/zipball/18a72b837b845bf9a2ad2c0050eaf864a22b7550", "reference": "18a72b837b845bf9a2ad2c0050eaf864a22b7550", "shasum": ""}, "require": {"ext-gmp": "*", "simplito/bn-php": "~1.1.0"}, "require-dev": {"phpbench/phpbench": "@dev", "phpunit/phpunit": "*"}, "type": "library", "autoload": {"psr-4": {"Elliptic\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Simplito Team", "email": "s.s<PERSON><PERSON><PERSON><PERSON>@simplito.com", "homepage": "https://simplito.com"}], "description": "Fast elliptic curve cryptography", "homepage": "https://github.com/simplito/elliptic-php", "keywords": ["Curve25519", "ECDSA", "Ed25519", "EdDSA", "cryptography", "curve", "curve25519-weier", "ecc", "ecdh", "elliptic", "nistp192", "nistp224", "nistp256", "nistp384", "nistp521", "secp256k1"], "support": {"issues": "https://github.com/simplito/elliptic-php/issues", "source": "https://github.com/simplito/elliptic-php/tree/1.0.9"}, "time": "2021-10-19T08:42:33+00:00"}, {"name": "spatie/eloquent-sortable", "version": "3.11.0", "source": {"type": "git", "url": "https://github.com/spatie/eloquent-sortable.git", "reference": "b06fa886559f8d40e31c8a69fd32bd45401dc5da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/eloquent-sortable/zipball/b06fa886559f8d40e31c8a69fd32bd45401dc5da", "reference": "b06fa886559f8d40e31c8a69fd32bd45401dc5da", "shasum": ""}, "require": {"illuminate/database": "^6.0|^7.0|^8.0", "illuminate/support": "^6.0|^7.0|^8.0", "php": "^7.3|^8.0"}, "require-dev": {"orchestra/testbench": "^4.0|^5.0|^6.0", "phpunit/phpunit": "^8.0|^9.0"}, "type": "library", "extra": {"laravel": {"providers": ["Spatie\\EloquentSortable\\EloquentSortableServiceProvider"]}}, "autoload": {"psr-4": {"Spatie\\EloquentSortable\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Sortable behaviour for eloquent models", "homepage": "https://github.com/spatie/eloquent-sortable", "keywords": ["behaviour", "eloquent", "laravel", "model", "sort", "sortable"], "support": {"issues": "https://github.com/spatie/eloquent-sortable/issues", "source": "https://github.com/spatie/eloquent-sortable/tree/3.11.0"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2021-01-18T00:32:12+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v6.3.0", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/8a5d5072dca8f48460fce2f4131fcc495eec654c", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c", "shasum": ""}, "require": {"egulias/email-validator": "^2.0|^3.1", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "^1.0", "symfony/phpunit-bridge": "^4.4|^5.4"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "support": {"issues": "https://github.com/swiftmailer/swiftmailer/issues", "source": "https://github.com/swiftmailer/swiftmailer/tree/v6.3.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/swiftmailer/swiftmailer", "type": "tidelift"}], "abandoned": "symfony/mailer", "time": "2021-10-18T15:26:12+00:00"}, {"name": "swoole/ide-helper", "version": "4.8.4", "source": {"type": "git", "url": "https://github.com/swoole/ide-helper.git", "reference": "feff48c90c87674ee7f1d40dd055edf9a212ad88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swoole/ide-helper/zipball/feff48c90c87674ee7f1d40dd055edf9a212ad88", "reference": "feff48c90c87674ee7f1d40dd055edf9a212ad88", "shasum": ""}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Team Swoole", "email": "<EMAIL>"}], "description": "IDE help files for Swoole.", "support": {"issues": "https://github.com/swoole/ide-helper/issues", "source": "https://github.com/swoole/ide-helper/tree/4.8.4"}, "funding": [{"url": "https://gitee.com/swoole/swoole?donate=true", "type": "custom"}, {"url": "https://github.com/swoole", "type": "github"}], "time": "2021-12-17T05:37:49+00:00"}, {"name": "symfony/console", "version": "v4.3.4", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "de63799239b3881b8a08f8481b22348f77ed7b36"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/de63799239b3881b8a08f8481b22348f77ed7b36", "reference": "de63799239b3881b8a08f8481b22348f77ed7b36", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/service-contracts": "^1.1"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/event-dispatcher": "<4.3", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/event-dispatcher": "^4.3", "symfony/lock": "~3.4|~4.0", "symfony/process": "~3.4|~4.0", "symfony/var-dumper": "^4.3"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/console/tree/v4.3.4"}, "time": "2019-08-26T08:26:39+00:00"}, {"name": "symfony/css-selector", "version": "v5.4.0", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "44b933f98bb4b5220d10bed9ce5662f8c2d13dcc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/44b933f98bb4b5220d10bed9ce5662f8c2d13dcc", "reference": "44b933f98bb4b5220d10bed9ce5662f8c2d13dcc", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v5.4.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-09-09T08:06:01+00:00"}, {"name": "symfony/debug", "version": "v4.4.31", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "43ede438d4cb52cd589ae5dc070e9323866ba8e0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/43ede438d4cb52cd589ae5dc070e9323866ba8e0", "reference": "43ede438d4cb52cd589ae5dc070e9323866ba8e0", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "^1|^2|^3"}, "conflict": {"symfony/http-kernel": "<3.4"}, "require-dev": {"symfony/http-kernel": "^3.4|^4.0|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/debug/tree/v4.4.31"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-09-24T13:30:14+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "6f981ee24cf69ee7ce9736146d1c57c2780598a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/6f981ee24cf69ee7ce9736146d1c57c2780598a8", "reference": "6f981ee24cf69ee7ce9736146d1c57c2780598a8", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-07-12T14:48:14+00:00"}, {"name": "symfony/dom-crawler", "version": "v5.4.0", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "5b06626e940a3ad54e573511d64d4e00dc8d0fd8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/5b06626e940a3ad54e573511d64d4e00dc8d0fd8", "reference": "5b06626e940a3ad54e573511d64d4e00dc8d0fd8", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"masterminds/html5": "<2.6"}, "require-dev": {"masterminds/html5": "^2.6", "symfony/css-selector": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/css-selector": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases DOM navigation for HTML and XML documents", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dom-crawler/tree/v5.4.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-23T10:19:22+00:00"}, {"name": "symfony/error-handler", "version": "v4.4.34", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "17785c374645def1e884d8ec49976c156c61db4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/17785c374645def1e884d8ec49976c156c61db4d", "reference": "17785c374645def1e884d8ec49976c156c61db4d", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "^1|^2|^3", "symfony/debug": "^4.4.5", "symfony/var-dumper": "^4.4|^5.0"}, "require-dev": {"symfony/http-kernel": "^4.4|^5.0", "symfony/serializer": "^4.4|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v4.4.34"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-12T14:57:39+00:00"}, {"name": "symfony/event-dispatcher", "version": "v4.4.34", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "1a024b45369c9d55d76b6b8a241bd20c9ea1cbd8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/1a024b45369c9d55d76b6b8a241bd20c9ea1cbd8", "reference": "1a024b45369c9d55d76b6b8a241bd20c9ea1cbd8", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/event-dispatcher-contracts": "^1.1", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/dependency-injection": "<3.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "1.1"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/error-handler": "~3.4|~4.4", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.34"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-15T14:42:25+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v1.1.11", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "01e9a4efac0ee33a05dfdf93b346f62e7d0e998c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/01e9a4efac0ee33a05dfdf93b346f62e7d0e998c", "reference": "01e9a4efac0ee33a05dfdf93b346f62e7d0e998c", "shasum": ""}, "require": {"php": ">=7.1.3"}, "suggest": {"psr/event-dispatcher": "", "symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v1.1.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-23T15:25:38+00:00"}, {"name": "symfony/finder", "version": "v4.4.30", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "70362f1e112280d75b30087c7598b837c1b468b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/70362f1e112280d75b30087c7598b837c1b468b6", "reference": "70362f1e112280d75b30087c7598b837c1b468b6", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v4.4.30"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-08-04T20:31:23+00:00"}, {"name": "symfony/http-client-contracts", "version": "v2.5.0", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "ec82e57b5b714dbb69300d348bd840b345e24166"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/ec82e57b5b714dbb69300d348bd840b345e24166", "reference": "ec82e57b5b714dbb69300d348bd840b345e24166", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/http-client-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-03T09:24:47+00:00"}, {"name": "symfony/http-foundation", "version": "v4.4.34", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "f4cbbb6fc428588ce8373802461e7fe84e6809ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/f4cbbb6fc428588ce8373802461e7fe84e6809ab", "reference": "f4cbbb6fc428588ce8373802461e7fe84e6809ab", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/mime": "^4.3|^5.0", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"predis/predis": "~1.0", "symfony/expression-language": "^3.4|^4.0|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v4.4.34"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-04T12:23:33+00:00"}, {"name": "symfony/http-kernel", "version": "v4.4.35", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "fb793f1381c34b79a43596a532a6a49bd729c9db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/fb793f1381c34b79a43596a532a6a49bd729c9db", "reference": "fb793f1381c34b79a43596a532a6a49bd729c9db", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "^1|^2", "symfony/error-handler": "^4.4", "symfony/event-dispatcher": "^4.4", "symfony/http-client-contracts": "^1.1|^2", "symfony/http-foundation": "^4.4.30|^5.3.7", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/browser-kit": "<4.3", "symfony/config": "<3.4", "symfony/console": ">=5", "symfony/dependency-injection": "<4.3", "symfony/translation": "<4.2", "twig/twig": "<1.43|<2.13,>=2"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^4.3|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0", "symfony/css-selector": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^4.3|^5.0", "symfony/dom-crawler": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/routing": "^3.4|^4.0|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/templating": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2|^5.0", "symfony/translation-contracts": "^1.1|^2", "twig/twig": "^1.43|^2.13|^3.0.4"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v4.4.35"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-24T08:40:10+00:00"}, {"name": "symfony/mime", "version": "v5.4.0", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "d4365000217b67c01acff407573906ff91bcfb34"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/d4365000217b67c01acff407573906ff91bcfb34", "reference": "d4365000217b67c01acff407573906ff91bcfb34", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<4.4"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/property-access": "^4.4|^5.1|^6.0", "symfony/property-info": "^4.4|^5.1|^6.0", "symfony/serializer": "^5.2|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v5.4.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-23T10:19:22+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.23.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "46cd95797e9df938fdd2b03693b5fca5e64b01ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/46cd95797e9df938fdd2b03693b5fca5e64b01ce", "reference": "46cd95797e9df938fdd2b03693b5fca5e64b01ce", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.23.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-02-19T12:13:01+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.23.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "63b5bb7db83e5673936d6e3b8b3e022ff6474933"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/63b5bb7db83e5673936d6e3b8b3e022ff6474933", "reference": "63b5bb7db83e5673936d6e3b8b3e022ff6474933", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-iconv/tree/v1.23.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-05-27T09:27:20+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.23.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "65bd267525e82759e7d8c4e8ceea44f398838e65"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/65bd267525e82759e7d8c4e8ceea44f398838e65", "reference": "65bd267525e82759e7d8c4e8ceea44f398838e65", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.23.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-05-27T09:27:20+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.23.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "8590a5f561694770bdcd3f9b5c69dde6945028e8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/8590a5f561694770bdcd3f9b5c69dde6945028e8", "reference": "8590a5f561694770bdcd3f9b5c69dde6945028e8", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.23.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-02-19T12:13:01+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.23.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "9174a3d80210dca8daa7f31fec659150bbeabfc6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/9174a3d80210dca8daa7f31fec659150bbeabfc6", "reference": "9174a3d80210dca8daa7f31fec659150bbeabfc6", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.23.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-05-27T12:26:48+00:00"}, {"name": "symfony/polyfill-php56", "version": "v1.20.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php56.git", "reference": "54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php56/zipball/54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675", "reference": "54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "metapackage", "extra": {"branch-alias": {"dev-main": "1.20-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 5.6+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php56/tree/v1.20.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-23T14:02:19+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.23.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "9a142215a36a3888e30d0a9eeea9766764e96976"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/9a142215a36a3888e30d0a9eeea9766764e96976", "reference": "9a142215a36a3888e30d0a9eeea9766764e96976", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php72\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.23.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-05-27T09:17:38+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.23.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "fba8933c384d6476ab14fb7b8526e5287ca7e010"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/fba8933c384d6476ab14fb7b8526e5287ca7e010", "reference": "fba8933c384d6476ab14fb7b8526e5287ca7e010", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.23.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-02-19T12:13:01+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.23.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "1100343ed1a92e3a38f9ae122fc0eb21602547be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/1100343ed1a92e3a38f9ae122fc0eb21602547be", "reference": "1100343ed1a92e3a38f9ae122fc0eb21602547be", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.23.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-07-28T13:41:28+00:00"}, {"name": "symfony/process", "version": "v4.4.35", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "c2098705326addae6e6742151dfade47ac71da1b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/c2098705326addae6e6742151dfade47ac71da1b", "reference": "c2098705326addae6e6742151dfade47ac71da1b", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v4.4.35"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-22T22:36:24+00:00"}, {"name": "symfony/routing", "version": "v4.4.34", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "fc9dda0c8496f8ef0a89805c2eabfc43b8cef366"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/fc9dda0c8496f8ef0a89805c2eabfc43b8cef366", "reference": "fc9dda0c8496f8ef0a89805c2eabfc43b8cef366", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/config": "<4.2", "symfony/dependency-injection": "<3.4", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "^1.10.4", "psr/log": "^1|^2|^3", "symfony/config": "^4.2|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v4.4.34"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-04T12:23:33+00:00"}, {"name": "symfony/service-contracts", "version": "v1.1.11", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "633df678bec3452e04a7b0337c9bcfe7354124b3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/633df678bec3452e04a7b0337c9bcfe7354124b3", "reference": "633df678bec3452e04a7b0337c9bcfe7354124b3", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/container": "^1.0"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v1.1.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-04T13:32:43+00:00"}, {"name": "symfony/translation", "version": "v4.4.34", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "26d330720627b234803595ecfc0191eeabc65190"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/26d330720627b234803595ecfc0191eeabc65190", "reference": "26d330720627b234803595ecfc0191eeabc65190", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^1.1.6|^2"}, "conflict": {"symfony/config": "<3.4", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4", "symfony/yaml": "<3.4"}, "provide": {"symfony/translation-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/finder": "~2.8|~3.0|~4.0|^5.0", "symfony/http-kernel": "^4.4", "symfony/intl": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v4.4.34"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-04T12:23:33+00:00"}, {"name": "symfony/translation-contracts", "version": "v2.5.0", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "d28150f0f44ce854e942b671fc2620a98aae1b1e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/d28150f0f44ce854e942b671fc2620a98aae1b1e", "reference": "d28150f0f44ce854e942b671fc2620a98aae1b1e", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-08-17T14:20:01+00:00"}, {"name": "symfony/var-dumper", "version": "v4.4.34", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "2d0c056b2faaa3d785bdbd5adecc593a5be9c16e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/2d0c056b2faaa3d785bdbd5adecc593a5be9c16e", "reference": "2d0c056b2faaa3d785bdbd5adecc593a5be9c16e", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.5", "symfony/polyfill-php80": "^1.16"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/console": "<3.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^3.4|^4.0|^5.0", "symfony/process": "^4.4|^5.0", "twig/twig": "^1.43|^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v4.4.34"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-12T10:50:54+00:00"}, {"name": "tijsverkoyen/css-to-inline-styles", "version": "2.2.4", "source": {"type": "git", "url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "reference": "da444caae6aca7a19c0c140f68c6182e337d5b1c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/da444caae6aca7a19c0c140f68c6182e337d5b1c", "reference": "da444caae6aca7a19c0c140f68c6182e337d5b1c", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^5.5 || ^7.0 || ^8.0", "symfony/css-selector": "^2.7 || ^3.0 || ^4.0 || ^5.0 || ^6.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^7.5 || ^8.5.21 || ^9.5.10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"TijsVerkoyen\\CssToInlineStyles\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CssToInlineStyles is a class that enables you to convert HTML-pages/files into HTML-pages/files with inline styles. This is very useful when you're sending emails.", "homepage": "https://github.com/tijsverkoyen/CssToInlineStyles", "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/2.2.4"}, "time": "2021-12-08T09:12:39+00:00"}, {"name": "torann/geoip", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/Torann/laravel-geoip.git", "reference": "15c7cb3d2edcfbfd7e8cd6f435defc2352df40d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Torann/laravel-geoip/zipball/15c7cb3d2edcfbfd7e8cd6f435defc2352df40d2", "reference": "15c7cb3d2edcfbfd7e8cd6f435defc2352df40d2", "shasum": ""}, "require": {"illuminate/console": "^6.0|^7.0", "illuminate/support": "^6.0|^7.0", "php": "^7.2"}, "require-dev": {"geoip2/geoip2": "~2.1", "mockery/mockery": "^1.3", "phpunit/phpunit": "^8.0", "vlucas/phpdotenv": "^4.0"}, "suggest": {"geoip2/geoip2": "Required to use the MaxMind database or web service with GeoIP (~2.1).", "monolog/monolog": "Allows for storing location not found errors to the log"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}, "laravel": {"providers": ["Torann\\GeoIP\\GeoIPServiceProvider"], "aliases": {"GeoIP": "Torann\\GeoIP\\Facades\\GeoIP"}}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Torann\\GeoIP\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Support for multiple GeoIP services.", "keywords": ["IP API", "geoip", "geolocation", "infoDB", "laravel", "location", "maxmind"], "support": {"issues": "https://github.com/Torann/laravel-geoip/issues", "source": "https://github.com/Torann/laravel-geoip/tree/1.2.1"}, "time": "2020-03-10T18:35:08+00:00"}, {"name": "tymon/jwt-auth", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/tymondesigns/jwt-auth.git", "reference": "e588cb719539366c0e2f6017f975379cb73e9680"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tymondesigns/jwt-auth/zipball/e588cb719539366c0e2f6017f975379cb73e9680", "reference": "e588cb719539366c0e2f6017f975379cb73e9680", "shasum": ""}, "require": {"illuminate/auth": "^5.2|^6|^7|^8", "illuminate/contracts": "^5.2|^6|^7|^8", "illuminate/http": "^5.2|^6|^7|^8", "illuminate/support": "^5.2|^6|^7|^8", "lcobucci/jwt": "<3.4", "namshi/jose": "^7.0", "nesbot/carbon": "^1.0|^2.0", "php": "^5.5.9|^7.0"}, "require-dev": {"illuminate/console": "^5.2|^6|^7|^8", "illuminate/database": "^5.2|^6|^7|^8", "illuminate/routing": "^5.2|^6|^7|^8", "mockery/mockery": ">=0.9.9", "phpunit/phpunit": "~4.8|~6.0"}, "type": "library", "extra": {"branch-alias": {"dev-develop": "1.0-dev"}, "laravel": {"aliases": {"JWTAuth": "Tymon\\JWTAuth\\Facades\\JWTAuth", "JWTFactory": "Tymon\\JWTAuth\\Facades\\JWTFactory"}, "providers": ["Tymon\\JWTAuth\\Providers\\LaravelServiceProvider"]}}, "autoload": {"psr-4": {"Tymon\\JWTAuth\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://tymon.xyz", "role": "Developer"}], "description": "JSON Web Token Authentication for <PERSON><PERSON> and <PERSON><PERSON>", "homepage": "https://github.com/tymondesigns/jwt-auth", "keywords": ["Authentication", "JSON Web Token", "auth", "jwt", "laravel"], "support": {"issues": "https://github.com/tymondesigns/jwt-auth/issues", "source": "https://github.com/tymondesigns/jwt-auth"}, "funding": [{"url": "https://www.patreon.com/seantymon", "type": "patreon"}], "time": "2020-11-27T12:32:42+00:00"}, {"name": "vlucas/phpdotenv", "version": "v3.6.10", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "5b547cdb25825f10251370f57ba5d9d924e6f68e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/5b547cdb25825f10251370f57ba5d9d924e6f68e", "reference": "5b547cdb25825f10251370f57ba5d9d924e6f68e", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0", "phpoption/phpoption": "^1.5.2", "symfony/polyfill-ctype": "^1.17"}, "require-dev": {"ext-filter": "*", "ext-pcre": "*", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.21"}, "suggest": {"ext-filter": "Required to use the boolean validator.", "ext-pcre": "Required to use most of the library."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v3.6.10"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2021-12-12T23:02:06+00:00"}, {"name": "web3p/ethereum-tx", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/web3p/ethereum-tx.git", "reference": "8dc1adc10ae45ac440c43ac32a3c5adf63ed8cef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/web3p/ethereum-tx/zipball/8dc1adc10ae45ac440c43ac32a3c5adf63ed8cef", "reference": "8dc1adc10ae45ac440c43ac32a3c5adf63ed8cef", "shasum": ""}, "require": {"kornrunner/keccak": "~1", "php": "^7.1|^8.0", "simplito/elliptic-php": "~1.0.6", "web3p/ethereum-util": "~0.1.3", "web3p/rlp": "0.3.4"}, "require-dev": {"phpunit/phpunit": "~7|~8.0"}, "default-branch": true, "type": "library", "autoload": {"psr-4": {"Web3p\\EthereumTx\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "sc0Vu", "email": "<EMAIL>"}], "description": "Ethereum transaction library in PHP.", "support": {"issues": "https://github.com/web3p/ethereum-tx/issues", "source": "https://github.com/web3p/ethereum-tx/tree/0.4.3"}, "time": "2021-09-01T05:13:20+00:00"}, {"name": "web3p/ethereum-util", "version": "0.1.3", "source": {"type": "git", "url": "https://github.com/web3p/ethereum-util.git", "reference": "77a860f35028eae57cd7e7a044ab6c11ffe1ad9e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/web3p/ethereum-util/zipball/77a860f35028eae57cd7e7a044ab6c11ffe1ad9e", "reference": "77a860f35028eae57cd7e7a044ab6c11ffe1ad9e", "shasum": ""}, "require": {"kornrunner/keccak": "~1", "php": "^7.1 | ^8.0", "simplito/elliptic-php": "~1.0.6"}, "require-dev": {"phpunit/phpunit": "~7 | ~8.0"}, "type": "library", "autoload": {"psr-4": {"Web3p\\EthereumUtil\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "sc0Vu", "email": "<EMAIL>"}], "description": "A collection of utility functions for Ethereum written in PHP.", "support": {"issues": "https://github.com/web3p/ethereum-util/issues", "source": "https://github.com/web3p/ethereum-util/tree/0.1.3"}, "time": "2021-06-05T05:35:13+00:00"}, {"name": "web3p/rlp", "version": "0.3.4", "source": {"type": "git", "url": "https://github.com/web3p/rlp.git", "reference": "1653af23142863b490bdf22c6d0335bdb588c983"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/web3p/rlp/zipball/1653af23142863b490bdf22c6d0335bdb588c983", "reference": "1653af23142863b490bdf22c6d0335bdb588c983", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^7.1 | ^8.0"}, "require-dev": {"phpunit/phpunit": "~7|~8.0"}, "type": "library", "autoload": {"psr-4": {"Web3p\\RLP\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "sc0Vu", "email": "<EMAIL>"}], "description": "Recursive Length Prefix Encoding in PHP.", "support": {"issues": "https://github.com/web3p/rlp/issues", "source": "https://github.com/web3p/rlp/tree/0.3.4"}, "time": "2021-08-30T10:22:03+00:00"}, {"name": "webmozart/assert", "version": "1.10.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "6964c76c7804814a842473e0c8fd15bab0f18e25"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/6964c76c7804814a842473e0c8fd15bab0f18e25", "reference": "6964c76c7804814a842473e0c8fd15bab0f18e25", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.10.0"}, "time": "2021-03-09T10:59:23+00:00"}, {"name": "workerman/channel", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/walkor/channel.git", "reference": "3df772d0d20d4cebfcfd621c33d1a1ab732db523"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/channel/zipball/3df772d0d20d4cebfcfd621c33d1a1ab732db523", "reference": "3df772d0d20d4cebfcfd621c33d1a1ab732db523", "shasum": ""}, "require": {"workerman/workerman": ">=4.0.12"}, "type": "library", "autoload": {"psr-4": {"Channel\\": "./src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "homepage": "http://www.workerman.net", "support": {"issues": "https://github.com/walkor/channel/issues", "source": "https://github.com/walkor/channel/tree/v1.1.0"}, "time": "2021-02-08T02:45:42+00:00"}, {"name": "workerman/gateway-worker", "version": "v3.0.22", "source": {"type": "git", "url": "https://github.com/walkor/GatewayWorker.git", "reference": "a615036c482d11f68b693998575e804752ef9068"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/GatewayWorker/zipball/a615036c482d11f68b693998575e804752ef9068", "reference": "a615036c482d11f68b693998575e804752ef9068", "shasum": ""}, "require": {"workerman/workerman": ">=3.5.0"}, "type": "library", "autoload": {"psr-4": {"GatewayWorker\\": "./src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "homepage": "http://www.workerman.net", "keywords": ["communication", "distributed"], "support": {"issues": "https://github.com/walkor/GatewayWorker/issues", "source": "https://github.com/walkor/GatewayWorker/tree/v3.0.22"}, "funding": [{"url": "https://opencollective.com/walkor", "type": "open_collective"}, {"url": "https://www.patreon.com/walkor", "type": "patreon"}], "time": "2021-12-23T13:13:09+00:00"}, {"name": "workerman/gatewayclient", "version": "v3.0.14", "source": {"type": "git", "url": "https://github.com/walkor/GatewayClient.git", "reference": "4362468d68251015b2b385c310252afb4d6648ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/GatewayClient/zipball/4362468d68251015b2b385c310252afb4d6648ed", "reference": "4362468d68251015b2b385c310252afb4d6648ed", "shasum": ""}, "type": "library", "autoload": {"psr-4": {"GatewayClient\\": "./"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "homepage": "http://www.workerman.net", "support": {"issues": "https://github.com/walkor/GatewayClient/issues", "source": "https://github.com/walkor/GatewayClient/tree/v3.0.14"}, "time": "2021-11-29T07:03:50+00:00"}, {"name": "workerman/http-client", "version": "v0.1.7", "source": {"type": "git", "url": "https://github.com/walkor/http-client.git", "reference": "f4a4365c44a2837035f290c593b26343a6058ba1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/http-client/zipball/f4a4365c44a2837035f290c593b26343a6058ba1", "reference": "f4a4365c44a2837035f290c593b26343a6058ba1", "shasum": ""}, "require": {"workerman/psr7": ">=1.4.3", "workerman/workerman": ">=3.5.0"}, "type": "library", "autoload": {"psr-4": {"Workerman\\Http\\": "./src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "homepage": "http://www.workerman.net", "support": {"issues": "https://github.com/walkor/http-client/issues", "source": "https://github.com/walkor/http-client/tree/v0.1.7"}, "time": "2020-11-24T01:44:51+00:00"}, {"name": "workerman/psr7", "version": "v1.4.4", "source": {"type": "git", "url": "https://github.com/walkor/psr7.git", "reference": "8f163224ed5bb93fb210da9211651fcd88acb97b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/psr7/zipball/8f163224ed5bb93fb210da9211651fcd88acb97b", "reference": "8f163224ed5bb93fb210da9211651fcd88acb97b", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-4": {"Workerman\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "request", "response", "stream", "uri", "url"], "support": {"source": "https://github.com/walkor/psr7/tree/v1.4.4"}, "time": "2020-08-31T09:54:31+00:00"}, {"name": "workerman/workerman", "version": "v4.0.26", "source": {"type": "git", "url": "https://github.com/walkor/workerman.git", "reference": "27573e9f985f9ec0665b1f9924308d359bd0fdaa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/workerman/zipball/27573e9f985f9ec0665b1f9924308d359bd0fdaa", "reference": "27573e9f985f9ec0665b1f9924308d359bd0fdaa", "shasum": ""}, "require": {"php": ">=5.3"}, "suggest": {"ext-event": "For better performance. "}, "type": "library", "autoload": {"psr-4": {"Workerman\\": "./"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "http://www.workerman.net", "role": "Developer"}], "description": "An asynchronous event driven PHP framework for easily building fast, scalable network applications.", "homepage": "http://www.workerman.net", "keywords": ["asynchronous", "event-loop"], "support": {"email": "<EMAIL>", "forum": "http://wenda.workerman.net/", "issues": "https://github.com/walkor/workerman/issues", "source": "https://github.com/walkor/workerman", "wiki": "http://doc.workerman.net/"}, "funding": [{"url": "https://opencollective.com/walkor", "type": "open_collective"}, {"url": "https://www.patreon.com/walkor", "type": "patreon"}], "time": "2021-12-21T03:39:14+00:00"}, {"name": "zgldh/qiniu-laravel-storage", "version": "v0.10.4", "source": {"type": "git", "url": "https://github.com/zgldh/qiniu-laravel-storage.git", "reference": "9fc08350af8cc4ffbf61924a31bcfa23d2e96f8d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zgldh/qiniu-laravel-storage/zipball/9fc08350af8cc4ffbf61924a31bcfa23d2e96f8d", "reference": "9fc08350af8cc4ffbf61924a31bcfa23d2e96f8d", "shasum": ""}, "require": {"league/flysystem": "^1.0", "php": ">=5.3.3", "qiniu/php-sdk": "^7.2"}, "type": "library", "extra": {"laravel": {"providers": ["zgldh\\QiniuStorage\\QiniuFilesystemServiceProvider"]}}, "autoload": {"psr-4": {"zgldh\\QiniuStorage\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "zgldh", "email": "<EMAIL>", "role": "Original Developer"}, {"name": "ab<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://github.com/abcsun", "role": "Developer"}], "description": "Qiniu Resource (Cloud) Storage SDK for Laravel 5", "keywords": ["cloud", "laravel", "qiniu", "sdk", "storage"], "support": {"issues": "https://github.com/zgldh/qiniu-laravel-storage/issues", "source": "https://github.com/zgldh/qiniu-laravel-storage/tree/v0.10.4"}, "time": "2020-12-16T06:17:04+00:00"}], "packages-dev": [{"name": "beyondcode/laravel-dump-server", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/beyondcode/laravel-dump-server.git", "reference": "fcc88fa66895f8c1ff83f6145a5eff5fa2a0739a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/beyondcode/laravel-dump-server/zipball/fcc88fa66895f8c1ff83f6145a5eff5fa2a0739a", "reference": "fcc88fa66895f8c1ff83f6145a5eff5fa2a0739a", "shasum": ""}, "require": {"illuminate/console": "5.6.*|5.7.*|5.8.*|^6.0", "illuminate/http": "5.6.*|5.7.*|5.8.*|^6.0", "illuminate/support": "5.6.*|5.7.*|5.8.*|^6.0", "php": "^7.1", "symfony/var-dumper": "^4.1.1"}, "require-dev": {"larapack/dd": "^1.0", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"laravel": {"providers": ["BeyondCode\\DumpServer\\DumpServerServiceProvider"]}}, "autoload": {"psr-4": {"BeyondCode\\DumpServer\\": "src"}, "files": ["helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://beyondco.de", "role": "Developer"}], "description": "Symfony Var-Dump Server for Laravel", "homepage": "https://github.com/beyondcode/laravel-dump-server", "keywords": ["beyondcode", "laravel-dump-server"], "support": {"issues": "https://github.com/beyondcode/laravel-dump-server/issues", "source": "https://github.com/beyondcode/laravel-dump-server/tree/1.3.0"}, "time": "2019-08-11T13:17:40+00:00"}, {"name": "doctrine/instantiator", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "d56bf6102915de5702778fe20f2de3b2fe570b5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/d56bf6102915de5702778fe20f2de3b2fe570b5b", "reference": "d56bf6102915de5702778fe20f2de3b2fe570b5b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.13 || 1.0.0-alpha2", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.4.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2020-11-10T18:47:58+00:00"}, {"name": "filp/whoops", "version": "2.14.4", "source": {"type": "git", "url": "https://github.com/filp/whoops.git", "reference": "f056f1fe935d9ed86e698905a957334029899895"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/filp/whoops/zipball/f056f1fe935d9ed86e698905a957334029899895", "reference": "f056f1fe935d9ed86e698905a957334029899895", "shasum": ""}, "require": {"php": "^5.5.9 || ^7.0 || ^8.0", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "require-dev": {"mockery/mockery": "^0.9 || ^1.0", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.3", "symfony/var-dumper": "^2.6 || ^3.0 || ^4.0 || ^5.0"}, "suggest": {"symfony/var-dumper": "Pretty print complex values better with var-dumper available", "whoops/soap": "Formats errors as SOAP responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Whoops\\": "src/Whoops/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/filp", "role": "Developer"}], "description": "php error handling for cool kids", "homepage": "https://filp.github.io/whoops/", "keywords": ["error", "exception", "handling", "library", "throwable", "whoops"], "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.14.4"}, "funding": [{"url": "https://github.com/denis-so<PERSON><PERSON>", "type": "github"}], "time": "2021-10-03T12:00:00+00:00"}, {"name": "fzaninotto/faker", "version": "v1.9.2", "source": {"type": "git", "url": "https://github.com/fzaninotto/Faker.git", "reference": "848d8125239d7dbf8ab25cb7f054f1a630e68c2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fzaninotto/Faker/zipball/848d8125239d7dbf8ab25cb7f054f1a630e68c2e", "reference": "848d8125239d7dbf8ab25cb7f054f1a630e68c2e", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"ext-intl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7", "squizlabs/php_codesniffer": "^2.9.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "support": {"issues": "https://github.com/fzaninotto/Faker/issues", "source": "https://github.com/fzaninotto/Faker/tree/v1.9.2"}, "abandoned": true, "time": "2020-12-11T09:56:16+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "shasum": ""}, "require": {"php": "^5.3|^7.0|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.0.1"}, "time": "2020-07-09T08:09:16+00:00"}, {"name": "mockery/mockery", "version": "1.3.5", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "472fa8ca4e55483d55ee1e73c963718c4393791d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/472fa8ca4e55483d55ee1e73c963718c4393791d", "reference": "472fa8ca4e55483d55ee1e73c963718c4393791d", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": ">=5.6.0"}, "require-dev": {"phpunit/phpunit": "^5.7.10|^6.5|^7.5|^8.5|^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-0": {"Mockery": "library/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.astrumfutura.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://davedevelopment.co.uk"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.3.5"}, "time": "2021-09-13T15:33:03+00:00"}, {"name": "myclabs/deep-copy", "version": "1.10.2", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "776f831124e9c62e1a2c601ecc52e776d8bb7220"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/776f831124e9c62e1a2c601ecc52e776d8bb7220", "reference": "776f831124e9c62e1a2c601ecc52e776d8bb7220", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "replace": {"myclabs/deep-copy": "self.version"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^7.1"}, "type": "library", "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}, "files": ["src/DeepCopy/deep_copy.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.10.2"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2020-11-13T09:40:50+00:00"}, {"name": "nunomaduro/collision", "version": "v3.2.0", "source": {"type": "git", "url": "https://github.com/nunomaduro/collision.git", "reference": "f7c45764dfe4ba5f2618d265a6f1f9c72732e01d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nunomaduro/collision/zipball/f7c45764dfe4ba5f2618d265a6f1f9c72732e01d", "reference": "f7c45764dfe4ba5f2618d265a6f1f9c72732e01d", "shasum": ""}, "require": {"filp/whoops": "^2.1.4", "php": "^7.2.5 || ^8.0", "php-parallel-lint/php-console-highlighter": "0.5.*", "symfony/console": "~2.8|~3.3|~4.0"}, "require-dev": {"laravel/framework": "^6.0", "phpunit/phpunit": "^8.0 || ^9.0"}, "type": "library", "extra": {"laravel": {"providers": ["NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider"]}}, "autoload": {"psr-4": {"NunoMaduro\\Collision\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Cli error handling for console/command-line PHP applications.", "keywords": ["artisan", "cli", "command-line", "console", "error", "handling", "laravel", "laravel-zero", "php", "symfony"], "support": {"issues": "https://github.com/nunomaduro/collision/issues", "source": "https://github.com/nunomaduro/collision"}, "funding": [{"url": "https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=66BYDWAT92N6L", "type": "custom"}, {"url": "https://github.com/nunomaduro", "type": "github"}, {"url": "https://www.patreon.com/nunomaduro", "type": "patreon"}], "time": "2021-02-11T09:01:42+00:00"}, {"name": "phar-io/manifest", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "7761fcacf03b4d4f16e7ccb606d4879ca431fcf4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/7761fcacf03b4d4f16e7ccb606d4879ca431fcf4", "reference": "7761fcacf03b4d4f16e7ccb606d4879ca431fcf4", "shasum": ""}, "require": {"ext-dom": "*", "ext-phar": "*", "phar-io/version": "^2.0", "php": "^5.6 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/master"}, "time": "2018-07-08T19:23:20+00:00"}, {"name": "phar-io/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "45a2ec53a73c70ce41d55cedef9063630abaf1b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/45a2ec53a73c70ce41d55cedef9063630abaf1b6", "reference": "45a2ec53a73c70ce41d55cedef9063630abaf1b6", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/master"}, "time": "2018-07-08T19:19:57+00:00"}, {"name": "phpspec/prophecy", "version": "v1.10.3", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "451c3cd1418cf640de218914901e51b064abb093"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/451c3cd1418cf640de218914901e51b064abb093", "reference": "451c3cd1418cf640de218914901e51b064abb093", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2|^4.0|^5.0", "sebastian/comparator": "^1.2.3|^2.0|^3.0|^4.0", "sebastian/recursion-context": "^1.0|^2.0|^3.0|^4.0"}, "require-dev": {"phpspec/phpspec": "^2.5 || ^3.2", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5 || ^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10.x-dev"}}, "autoload": {"psr-4": {"Prophecy\\": "src/Prophecy"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.10.3"}, "time": "2020-03-05T15:02:03+00:00"}, {"name": "phpunit/php-code-coverage", "version": "6.1.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "807e6013b00af69b6c5d9ceb4282d0393dbb9d8d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/807e6013b00af69b6c5d9ceb4282d0393dbb9d8d", "reference": "807e6013b00af69b6c5d9ceb4282d0393dbb9d8d", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": "^7.1", "phpunit/php-file-iterator": "^2.0", "phpunit/php-text-template": "^1.2.1", "phpunit/php-token-stream": "^3.0", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^3.1 || ^4.0", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "suggest": {"ext-xdebug": "^2.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/master"}, "time": "2018-10-31T16:06:48+00:00"}, {"name": "phpunit/php-file-iterator", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "42c5ba5220e6904cbfe8b1a1bda7c0cfdc8c12f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/42c5ba5220e6904cbfe8b1a1bda7c0cfdc8c12f5", "reference": "42c5ba5220e6904cbfe8b1a1bda7c0cfdc8c12f5", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/2.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2021-12-02T12:42:26+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/1.2.1"}, "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "2.1.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "2454ae1765516d20c4ffe103d85a58a9a3bd5662"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-timer/zipball/2454ae1765516d20c4ffe103d85a58a9a3bd5662", "reference": "2454ae1765516d20c4ffe103d85a58a9a3bd5662", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebastian<PERSON>mann/php-timer/tree/2.1.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T08:20:02+00:00"}, {"name": "phpunit/php-token-stream", "version": "3.1.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "9c1da83261628cb24b6a6df371b6e312b3954768"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/9c1da83261628cb24b6a6df371b6e312b3954768", "reference": "9c1da83261628cb24b6a6df371b6e312b3954768", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-token-stream/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-token-stream/tree/3.1.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "abandoned": true, "time": "2021-07-26T12:15:06+00:00"}, {"name": "phpunit/phpunit", "version": "7.5.20", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "9467db479d1b0487c99733bb1e7944d32deded2c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/9467db479d1b0487c99733bb1e7944d32deded2c", "reference": "9467db479d1b0487c99733bb1e7944d32deded2c", "shasum": ""}, "require": {"doctrine/instantiator": "^1.1", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "myclabs/deep-copy": "^1.7", "phar-io/manifest": "^1.0.2", "phar-io/version": "^2.0", "php": "^7.1", "phpspec/prophecy": "^1.7", "phpunit/php-code-coverage": "^6.0.7", "phpunit/php-file-iterator": "^2.0.1", "phpunit/php-text-template": "^1.2.1", "phpunit/php-timer": "^2.1", "sebastian/comparator": "^3.0", "sebastian/diff": "^3.0", "sebastian/environment": "^4.0", "sebastian/exporter": "^3.1", "sebastian/global-state": "^2.0", "sebastian/object-enumerator": "^3.0.3", "sebastian/resource-operations": "^2.0", "sebastian/version": "^2.0.1"}, "conflict": {"phpunit/phpunit-mock-objects": "*"}, "require-dev": {"ext-pdo": "*"}, "suggest": {"ext-soap": "*", "ext-xdebug": "*", "phpunit/php-invoker": "^2.0"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "7.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/7.5.20"}, "time": "2020-01-08T08:45:45+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "1de8cd5c010cb153fcd68b8d0f64606f523f7619"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/1de8cd5c010cb153fcd68b8d0f64606f523f7619", "reference": "1de8cd5c010cb153fcd68b8d0f64606f523f7619", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/1.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T08:15:22+00:00"}, {"name": "sebastian/comparator", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "1071dfcef776a57013124ff35e1fc41ccd294758"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/1071dfcef776a57013124ff35e1fc41ccd294758", "reference": "1071dfcef776a57013124ff35e1fc41ccd294758", "shasum": ""}, "require": {"php": ">=7.1", "sebastian/diff": "^3.0", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/3.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T08:04:30+00:00"}, {"name": "sebastian/diff", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "14f72dd46eaf2f2293cbe79c93cc0bc43161a211"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/14f72dd46eaf2f2293cbe79c93cc0bc43161a211", "reference": "14f72dd46eaf2f2293cbe79c93cc0bc43161a211", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.0", "symfony/process": "^2 || ^3.3 || ^4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/3.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:59:04+00:00"}, {"name": "sebastian/environment", "version": "4.2.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "d47bbbad83711771f167c72d4e3f25f7fcc1f8b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastianbergmann/environment/zipball/d47bbbad83711771f167c72d4e3f25f7fcc1f8b0", "reference": "d47bbbad83711771f167c72d4e3f25f7fcc1f8b0", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.5"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/4.2.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:53:42+00:00"}, {"name": "sebastian/exporter", "version": "3.1.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "0c32ea2e40dbf59de29f3b49bf375176ce7dd8db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/0c32ea2e40dbf59de29f3b49bf375176ce7dd8db", "reference": "0c32ea2e40dbf59de29f3b49bf375176ce7dd8db", "shasum": ""}, "require": {"php": ">=7.0", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/3.1.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2021-11-11T13:51:24+00:00"}, {"name": "sebastian/global-state", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4", "reference": "e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/2.0.0"}, "time": "2017-04-27T15:39:26+00:00"}, {"name": "sebastian/object-enumerator", "version": "3.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/object-enumerator/zipball/e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2", "reference": "e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2", "shasum": ""}, "require": {"php": ">=7.0", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/3.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:40:27+00:00"}, {"name": "sebastian/object-reflector", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "9b8772b9cbd456ab45d4a598d2dd1a1bced6363d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/9b8772b9cbd456ab45d4a598d2dd1a1bced6363d", "reference": "9b8772b9cbd456ab45d4a598d2dd1a1bced6363d", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/1.1.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:37:18+00:00"}, {"name": "sebastian/recursion-context", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "367dcba38d6e1977be014dc4b22f47a484dac7fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/367dcba38d6e1977be014dc4b22f47a484dac7fb", "reference": "367dcba38d6e1977be014dc4b22f47a484dac7fb", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/3.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:34:24+00:00"}, {"name": "sebastian/resource-operations", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "31d35ca87926450c44eae7e2611d45a7a65ea8b3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/resource-operations/zipball/31d35ca87926450c44eae7e2611d45a7a65ea8b3", "reference": "31d35ca87926450c44eae7e2611d45a7a65ea8b3", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "support": {"issues": "https://github.com/sebastian<PERSON>mann/resource-operations/issues", "source": "https://github.com/sebastian<PERSON>mann/resource-operations/tree/2.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:30:19+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/master"}, "time": "2016-10-03T07:35:21+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "34a41e998c2183e22995f158c581e7b5e755ab9e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/34a41e998c2183e22995f158c581e7b5e755ab9e", "reference": "34a41e998c2183e22995f158c581e7b5e755ab9e", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.1"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2021-07-28T10:34:58+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": {"dcat-admin-extension/ueditor": 20, "web3p/ethereum-tx": 20}, "prefer-stable": true, "prefer-lowest": false, "platform": {"php": "^7.3", "ext-curl": "*", "ext-json": "*"}, "platform-dev": [], "plugin-api-version": "2.0.0"}