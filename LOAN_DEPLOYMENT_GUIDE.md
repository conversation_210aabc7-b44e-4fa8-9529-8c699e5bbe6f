# 🚀 贷款功能部署指南

## 📋 部署清单

### ✅ 已完成的文件

#### 1. 数据库迁移文件
- `database/migrations/2024_01_01_000001_create_load_product_table.php` - 贷款产品表
- `database/migrations/2024_01_01_000002_create_load_order_table.php` - 贷款订单表

#### 2. 模型类
- `app/Models/LoadProduct.php` - 贷款产品模型
- `app/Models/LoadOrder.php` - 贷款订单模型

#### 3. API控制器
- `app/Http/Controllers/Api/V1/LoadProductController.php` - 用户端产品API
- `app/Http/Controllers/Api/V1/LoadOrderController.php` - 用户端订单API

#### 4. 后台管理控制器
- `app/Admin/Controllers/LoadProductController.php` - 后台产品管理
- `app/Admin/Controllers/LoadOrderController.php` - 后台订单管理

#### 5. 业务服务类
- `app/Services/LoadService.php` - 贷款业务逻辑服务
- `app/Http/Requests/LoadOrderRequest.php` - 订单请求验证

#### 6. 定时任务
- `app/Console/Commands/UpdateOverdueLoans.php` - 逾期订单更新命令

#### 7. 配置文件
- `database/seeds/LoadProductSeeder.php` - 产品数据初始化
- `database/sql/loan_menu.sql` - 后台菜单配置

## 🔧 部署步骤

### 1. 运行数据库迁移

```bash
# 进入项目目录
cd htadmin.com_cFnwi

# 运行迁移
php artisan migrate

# 初始化贷款产品数据
php artisan db:seed --class=LoadProductSeeder
```

### 2. 配置后台菜单

方式一：执行SQL文件
```bash
mysql -u username -p database_name < database/sql/loan_menu.sql
```

方式二：在后台管理系统中手动添加菜单
1. 登录后台管理系统
2. 进入菜单管理
3. 添加主菜单：贷款管理
4. 添加子菜单：
   - 贷款产品 (URI: /load-products)
   - 贷款订单 (URI: /load-orders)

### 3. 配置定时任务

在服务器crontab中添加：
```bash
# 编辑crontab
crontab -e

# 添加逾期检查任务（每小时执行）
0 * * * * cd /path/to/htadmin.com_cFnwi && php artisan loan:update-overdue >> /dev/null 2>&1
```

### 4. 测试功能

运行测试脚本：
```bash
php test_loan_system.php
```

## 📡 API接口测试

### 用户端API测试

#### 1. 获取贷款产品列表
```bash
curl -X POST "http://your-domain/api/load/product/list" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

#### 2. 提交贷款申请
```bash
curl -X POST "http://your-domain/api/load/order/submit" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "product_id": 1,
    "amount": 1000,
    "card_url": "http://example.com/card.jpg",
    "card_back_url": "http://example.com/card_back.jpg",
    "capital_url": "http://example.com/capital.jpg"
  }'
```

#### 3. 获取用户订单列表
```bash
curl -X POST "http://your-domain/api/load/order/list" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"status": 0}'
```

### 后台管理测试

1. 访问 `http://your-domain/admin/load-products` - 贷款产品管理
2. 访问 `http://your-domain/admin/load-orders` - 贷款订单管理

## 🔍 功能验证

### 1. 贷款申请流程测试
1. 用户登录获取token
2. 调用产品列表API获取可用产品
3. 提交贷款申请
4. 后台审核通过
5. 检查用户USDT余额是否增加
6. 检查用户状态是否被冻结

### 2. 逾期检测测试
1. 创建一个测试订单并审核通过
2. 手动修改final_repay_time为过去时间
3. 运行逾期更新命令：`php artisan loan:update-overdue`
4. 检查订单状态是否变为逾期(4)
5. 检查逾期利息是否正确计算

### 3. 还款流程测试
1. 选择一个已审核通过的订单
2. 在后台执行还款操作
3. 检查订单状态是否变为已结清(3)
4. 检查用户冻结状态是否解除

## ⚠️ 注意事项

### 1. 依赖检查
确保以下模型类存在且字段正确：
- `User` 模型 (主键: user_id, 状态字段: status)
- `UserWallet` 模型 (字段: user_id, coin_name, usable_balance)
- `UserWalletLog` 模型 (钱包日志记录)

### 2. 权限配置
- 确保API路由有正确的认证中间件
- 配置后台管理权限
- 检查用户状态验证逻辑

### 3. 图片上传
- 确保图片上传功能正常
- 配置正确的存储路径
- 设置合适的文件大小限制

### 4. 错误处理
- 检查日志文件权限
- 配置错误通知
- 设置合适的超时时间

## 🔄 后续优化

### 1. 功能扩展
- 添加贷款申请邮件/短信通知
- 实现逾期提醒功能
- 添加贷款统计报表
- 配置风控规则

### 2. 性能优化
- 添加Redis缓存
- 优化数据库查询
- 实现异步处理

### 3. 安全加固
- 添加请求频率限制
- 实现IP白名单
- 加强数据验证

## 📊 监控指标

建议监控以下指标：
- 贷款申请数量
- 审核通过率
- 逾期率
- 平均放款金额
- 系统响应时间

## 🆘 故障排除

### 常见问题
1. **数据库连接失败** - 检查数据库配置
2. **权限不足** - 检查文件权限和用户权限
3. **API认证失败** - 检查JWT配置和token有效性
4. **钱包操作失败** - 检查UserWallet模型字段映射

### 日志位置
- Laravel日志: `storage/logs/laravel.log`
- 贷款业务日志: 搜索关键词 "loan", "LoadService"

## 📞 技术支持

如遇到问题，请提供：
1. 错误日志信息
2. 具体操作步骤
3. 环境配置信息
4. 数据库表结构
