<?php

use Illuminate\Database\Seeder;
use App\Models\LoadProduct;

class LoadProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // 清空现有数据
        LoadProduct::truncate();

        // 创建默认贷款产品
        $products = [
            [
                'amount_min' => 100.00,
                'amount_max' => 1000.00,
                'cycle_type' => 7,
                'repay_type' => 0,
                'status' => 1,
                'odds' => 1.5,
                'repay_org' => '平台自营',
                'is_freeze' => '1',
                'remark' => '7天短期贷款产品',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'amount_min' => 500.00,
                'amount_max' => 5000.00,
                'cycle_type' => 14,
                'repay_type' => 0,
                'status' => 1,
                'odds' => 2.0,
                'repay_org' => '平台自营',
                'is_freeze' => '1',
                'remark' => '14天中期贷款产品',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'amount_min' => 1000.00,
                'amount_max' => 10000.00,
                'cycle_type' => 30,
                'repay_type' => 0,
                'status' => 1,
                'odds' => 2.5,
                'repay_org' => '平台自营',
                'is_freeze' => '1',
                'remark' => '30天长期贷款产品',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'amount_min' => 2000.00,
                'amount_max' => 50000.00,
                'cycle_type' => 60,
                'repay_type' => 0,
                'status' => 1,
                'odds' => 3.0,
                'repay_org' => '平台自营',
                'is_freeze' => '1',
                'remark' => '60天超长期贷款产品',
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        foreach ($products as $product) {
            LoadProduct::create($product);
        }

        $this->command->info('贷款产品数据初始化完成！');
    }
}
