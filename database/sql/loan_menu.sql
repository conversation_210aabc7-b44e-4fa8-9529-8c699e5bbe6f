-- 贷款功能后台菜单配置
-- 请在后台管理系统中手动添加以下菜单项

-- 1. 添加贷款管理主菜单
INSERT INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `permission`, `created_at`, `updated_at`) 
VALUES (0, 100, '贷款管理', 'fa-credit-card', '', '', NOW(), NOW());

-- 获取刚插入的贷款管理菜单ID (假设为 @loan_menu_id)
SET @loan_menu_id = LAST_INSERT_ID();

-- 2. 添加贷款产品管理子菜单
INSERT INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `permission`, `created_at`, `updated_at`) 
VALUES (@loan_menu_id, 1, '贷款产品', 'fa-product-hunt', '/load-products', '', NOW(), NOW());

-- 3. 添加贷款订单管理子菜单
INSERT INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `permission`, `created_at`, `updated_at`) 
VALUES (@loan_menu_id, 2, '贷款订单', 'fa-list-alt', '/load-orders', '', NOW(), NOW());

-- 4. 添加权限配置 (如果系统使用权限管理)
-- 贷款产品权限
INSERT INTO `admin_permissions` (`name`, `slug`, `http_method`, `http_path`, `created_at`, `updated_at`) 
VALUES 
('贷款产品列表', 'load.product.index', 'GET', '/load-products', NOW(), NOW()),
('贷款产品创建', 'load.product.create', 'GET,POST', '/load-products/create', NOW(), NOW()),
('贷款产品编辑', 'load.product.edit', 'GET,PUT', '/load-products/*/edit', NOW(), NOW()),
('贷款产品删除', 'load.product.delete', 'DELETE', '/load-products/*', NOW(), NOW());

-- 贷款订单权限
INSERT INTO `admin_permissions` (`name`, `slug`, `http_method`, `http_path`, `created_at`, `updated_at`) 
VALUES 
('贷款订单列表', 'load.order.index', 'GET', '/load-orders', NOW(), NOW()),
('贷款订单详情', 'load.order.show', 'GET', '/load-orders/*', NOW(), NOW()),
('贷款订单审核', 'load.order.approve', 'GET,POST', '/load-orders/*/approve', NOW(), NOW()),
('贷款订单拒绝', 'load.order.reject', 'GET,POST', '/load-orders/*/reject', NOW(), NOW()),
('贷款订单还款', 'load.order.repay', 'GET,POST', '/load-orders/*/repay', NOW(), NOW());

-- 注意：
-- 1. 请根据实际的admin_menu表结构调整字段名
-- 2. 请根据实际的权限管理系统调整权限配置
-- 3. 菜单图标可以根据实际使用的图标库调整
-- 4. 执行前请备份数据库
