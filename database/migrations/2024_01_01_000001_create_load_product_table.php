<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLoadProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('load_product', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->decimal('amount_min', 10, 2)->comment('最小贷款额度');
            $table->decimal('amount_max', 10, 2)->comment('最大贷款额度');
            $table->bigInteger('cycle_type')->comment('贷款周期(天数)');
            $table->bigInteger('repay_type')->default(0)->comment('还款类型(0:到期一次还本息)');
            $table->bigInteger('status')->default(1)->comment('状态(0:未开启 1:已开启)');
            $table->decimal('odds', 10, 2)->comment('日利率(%)');
            $table->string('repay_org')->nullable()->comment('还款机构');
            $table->string('is_freeze', 10)->default('1')->comment('是否冻结(1:正常 2:冻结)');
            $table->text('remark')->nullable()->comment('备注');
            $table->timestamps();
            
            $table->index(['status', 'is_freeze']);
            $table->comment('贷款产品表');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('load_product');
    }
}
