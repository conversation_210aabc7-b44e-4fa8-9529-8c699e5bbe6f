<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLoadOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('load_order', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('pro_id')->comment('贷款产品ID');
            $table->bigInteger('user_id')->comment('用户ID');
            $table->decimal('amount', 10, 2)->comment('申请金额');
            $table->decimal('rate', 10, 2)->comment('贷款利率');
            $table->decimal('interest', 10, 2)->comment('利息');
            $table->integer('status')->default(0)->comment('状态(0:待审核 1:审核通过 2:审核拒绝 3:已结清 4:已逾期)');
            $table->datetime('final_repay_time')->nullable()->comment('最后还款日');
            $table->datetime('disburse_time')->nullable()->comment('放款日期');
            $table->datetime('return_time')->nullable()->comment('还款日期');
            $table->decimal('disburse_amount', 10, 2)->nullable()->comment('审批金额');
            $table->string('admin_parent_ids')->nullable()->comment('后台代理IDs');
            $table->string('card_url')->nullable()->comment('身份证正面');
            $table->string('card_back_url')->nullable()->comment('身份证反面');
            $table->string('capital_url')->nullable()->comment('手持身份证');
            $table->string('order_no', 50)->unique()->comment('订单号');
            $table->integer('cycle_type')->comment('还款周期(天数)');
            $table->decimal('overdue_interest', 10, 2)->default(0)->comment('逾期利息');
            $table->integer('overdue_days')->default(0)->comment('逾期天数');
            $table->text('refuse_reason')->nullable()->comment('拒绝原因');
            $table->timestamps();
            
            $table->index(['user_id', 'status']);
            $table->index(['status', 'final_repay_time']);
            $table->index('order_no');
            $table->foreign('pro_id')->references('id')->on('load_product');
            $table->foreign('user_id')->references('user_id')->on('users');
            $table->comment('贷款订单表');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('load_order');
    }
}
