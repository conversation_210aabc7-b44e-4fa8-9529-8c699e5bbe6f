<?php
require "bootstrap/app.php";

use App\Models\LoadProduct;
use App\Models\LoadOrder;
use App\Models\User;
use App\Services\LoadService;
use Illuminate\Support\Facades\DB;

echo "=== 贷款系统功能测试 ===\n\n";

try {
    // 1. 测试数据库连接
    echo "1. 测试数据库连接:\n";
    DB::connection()->getPdo();
    echo "✅ 数据库连接正常\n\n";

    // 2. 检查表是否存在
    echo "2. 检查数据库表:\n";
    $tables = ['load_product', 'load_order'];
    foreach ($tables as $table) {
        if (DB::getSchemaBuilder()->hasTable($table)) {
            echo "✅ 表 {$table} 存在\n";
        } else {
            echo "❌ 表 {$table} 不存在\n";
        }
    }
    echo "\n";

    // 3. 测试贷款产品模型
    echo "3. 测试贷款产品模型:\n";
    $productCount = LoadProduct::count();
    echo "贷款产品数量: {$productCount}\n";
    
    if ($productCount == 0) {
        echo "创建测试贷款产品...\n";
        $testProduct = LoadProduct::create([
            'amount_min' => 100.00,
            'amount_max' => 10000.00,
            'cycle_type' => 30,
            'repay_type' => 0,
            'status' => 1,
            'odds' => 2.5,
            'repay_org' => '测试还款机构',
            'is_freeze' => '1',
            'remark' => '测试贷款产品'
        ]);
        echo "✅ 测试产品创建成功，ID: {$testProduct->id}\n";
    } else {
        $product = LoadProduct::first();
        echo "✅ 产品模型测试正常，示例产品: {$product->amount_min}-{$product->amount_max} USDT\n";
    }
    echo "\n";

    // 4. 测试贷款订单模型
    echo "4. 测试贷款订单模型:\n";
    $orderCount = LoadOrder::count();
    echo "贷款订单数量: {$orderCount}\n";
    
    // 测试订单号生成
    $orderNo = LoadOrder::generateOrderNo();
    echo "✅ 订单号生成测试: {$orderNo}\n\n";

    // 5. 测试贷款服务
    echo "5. 测试贷款服务:\n";
    $loadService = new LoadService();
    $stats = $loadService->getStatistics();
    
    if (!empty($stats)) {
        echo "✅ 贷款统计数据:\n";
        echo "  总订单数: {$stats['total_orders']}\n";
        echo "  待审核: {$stats['pending_orders']}\n";
        echo "  已通过: {$stats['approved_orders']}\n";
        echo "  已逾期: {$stats['overdue_orders']}\n";
        echo "  已结清: {$stats['settled_orders']}\n";
        echo "  总放款金额: " . number_format($stats['total_loan_amount'], 2) . " USDT\n";
        echo "  总利息: " . number_format($stats['total_interest'], 2) . " USDT\n";
        echo "  逾期利息: " . number_format($stats['total_overdue_interest'], 2) . " USDT\n";
    } else {
        echo "❌ 贷款服务测试失败\n";
    }
    echo "\n";

    // 6. 测试利息计算
    echo "6. 测试利息计算:\n";
    $product = LoadProduct::first();
    if ($product) {
        $testAmount = 1000;
        $interest = $product->calculateInterest($testAmount);
        echo "✅ 利息计算测试:\n";
        echo "  贷款金额: {$testAmount} USDT\n";
        echo "  日利率: {$product->odds}%\n";
        echo "  贷款周期: {$product->cycle_type} 天\n";
        echo "  计算利息: " . number_format($interest, 2) . " USDT\n";
        echo "  还款总额: " . number_format($testAmount + $interest, 2) . " USDT\n";
    }
    echo "\n";

    // 7. 测试逾期更新
    echo "7. 测试逾期更新功能:\n";
    $result = $loadService->updateOverdueOrders();
    if ($result['success']) {
        echo "✅ " . $result['message'] . "\n";
    } else {
        echo "❌ " . $result['message'] . "\n";
    }
    echo "\n";

    echo "=== 测试完成 ===\n";
    echo "\n后续步骤:\n";
    echo "1. 运行数据库迁移: php artisan migrate\n";
    echo "2. 访问后台管理: /admin/load-products (贷款产品管理)\n";
    echo "3. 访问后台管理: /admin/load-orders (贷款订单管理)\n";
    echo "4. 测试API接口:\n";
    echo "   - POST /api/load/product/list (获取产品列表)\n";
    echo "   - POST /api/load/order/submit (提交贷款申请)\n";
    echo "   - POST /api/load/order/list (获取订单列表)\n";
    echo "5. 设置定时任务: php artisan loan:update-overdue\n";

} catch (\Exception $e) {
    echo "❌ 测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
?>
