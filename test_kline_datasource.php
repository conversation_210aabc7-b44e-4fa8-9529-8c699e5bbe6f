<?php
require "bootstrap/app.php";

echo "=== K线数据源切换功能测试 ===\n\n";

// 测试参数
$testSymbol = 'btcusdt';
$testPeriod = '1min';
$testLimit = 3;

echo "测试参数: 符号={$testSymbol}, 周期={$testPeriod}, 数量={$testLimit}\n\n";

// 1. 测试当前配置的数据源
$currentSource = config('coin.kline_data_source', 'huobi');
echo "1. 当前配置的数据源: {$currentSource}\n";

// 2. 测试helper函数
echo "2. 测试getKlineData函数:\n";
$data = getKlineData($testSymbol, $testPeriod, $testLimit);

if (!empty($data)) {
    echo "✅ 数据获取成功，获得 " . count($data) . " 条记录\n";
    echo "示例数据:\n";
    foreach (array_slice($data, 0, 2) as $index => $kline) {
        echo "  记录 " . ($index + 1) . ": ";
        echo "时间=" . date('Y-m-d H:i:s', $kline['id']) . ", ";
        echo "开盘=" . $kline['open'] . ", ";
        echo "收盘=" . $kline['close'] . ", ";
        echo "最高=" . $kline['high'] . ", ";
        echo "最低=" . $kline['low'] . "\n";
    }
} else {
    echo "❌ 数据获取失败\n";
}

echo "\n3. 测试火币数据源:\n";
$huobiData = getHuobiKlineData($testSymbol, $testPeriod, $testLimit);
if (!empty($huobiData)) {
    echo "✅ 火币数据获取成功，获得 " . count($huobiData) . " 条记录\n";
    echo "示例: 时间=" . date('Y-m-d H:i:s', $huobiData[0]['id']) . ", 收盘=" . $huobiData[0]['close'] . "\n";
} else {
    echo "❌ 火币数据获取失败\n";
}

echo "\n4. 测试币安数据源:\n";
$binanceData = getBinanceKlineData($testSymbol, $testPeriod, $testLimit);
if (!empty($binanceData)) {
    echo "✅ 币安数据获取成功，获得 " . count($binanceData) . " 条记录\n";
    echo "示例: 时间=" . date('Y-m-d H:i:s', $binanceData[0]['id']) . ", 收盘=" . $binanceData[0]['close'] . "\n";
} else {
    echo "❌ 币安数据获取失败\n";
}

// 5. 数据格式对比
if (!empty($huobiData) && !empty($binanceData)) {
    echo "\n5. 数据格式对比:\n";
    $huobiKeys = array_keys($huobiData[0]);
    $binanceKeys = array_keys($binanceData[0]);
    
    echo "火币数据字段: " . implode(', ', $huobiKeys) . "\n";
    echo "币安数据字段: " . implode(', ', $binanceKeys) . "\n";
    
    $commonKeys = array_intersect($huobiKeys, $binanceKeys);
    echo "共同字段: " . implode(', ', $commonKeys) . "\n";
    
    if (count($commonKeys) >= 6) { // 至少包含基本的OHLCV字段
        echo "✅ 数据格式兼容性良好\n";
    } else {
        echo "⚠️  数据格式可能需要调整\n";
    }
}

// 6. 测试WebSocket功能
echo "\n6. 测试WebSocket配置:\n";
$wsUrl = getWebSocketUrl();
echo "WebSocket URL: {$wsUrl}\n";

$testSymbols = ['btcusdt', 'ethusdt'];
$testPeriod = '1min';
$subscriptions = createWebSocketSubscriptions($testSymbols, $testPeriod);

echo "订阅消息数量: " . count($subscriptions) . "\n";
echo "示例订阅消息:\n";
foreach (array_slice($subscriptions, 0, 2) as $index => $sub) {
    echo "  消息 " . ($index + 1) . ": " . json_encode($sub) . "\n";
}

// 7. 测试消息处理
echo "\n7. 测试WebSocket消息处理:\n";
if ($currentSource === 'binance') {
    // 模拟币安WebSocket消息
    $mockBinanceMessage = json_encode([
        "e" => "kline",
        "E" => 1638747660000,
        "s" => "BTCUSDT",
        "k" => [
            "t" => 1638747660000,
            "T" => 1638747719999,
            "s" => "BTCUSDT",
            "i" => "1m",
            "f" => 100,
            "L" => 200,
            "o" => "47000.00",
            "c" => "47200.00",
            "h" => "47500.00",
            "l" => "46800.00",
            "v" => "26.12",
            "n" => 1250,
            "x" => false,
            "q" => "1234567.89",
            "V" => "13.06",
            "Q" => "617283.95",
            "B" => "123456"
        ]
    ]);

    $processed = processBinanceWebSocketMessage($mockBinanceMessage);
    if ($processed && $processed['type'] === 'kline') {
        echo "✅ 币安WebSocket消息处理成功\n";
        echo "处理结果: symbol={$processed['symbol']}, period={$processed['period']}\n";
        echo "数据: open={$processed['data']['open']}, close={$processed['data']['close']}\n";
    } else {
        echo "❌ 币安WebSocket消息处理失败\n";
    }
} else {
    echo "当前使用火币数据源，WebSocket消息处理保持原有逻辑\n";
}

echo "\n=== 测试完成 ===\n";
echo "\n使用说明:\n";
echo "1. 修改.env文件中的KLINE_DATA_SOURCE=binance 切换到币安\n";
echo "2. 修改.env文件中的KLINE_DATA_SOURCE=huobi 切换到火币\n";
echo "3. 重启K线进程以应用新配置\n";
echo "4. 所有K线脚本会自动使用新的数据源（包括WebSocket）\n";
echo "5. WebSocket会根据数据源自动连接到对应的服务器\n";
?>
