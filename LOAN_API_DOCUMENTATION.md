# 📡 贷款功能API文档

## 🔐 认证说明

所有用户端API都需要在请求头中包含JWT Token：
```
Authorization: Bearer {your_jwt_token}
```

## 📱 用户端API

### 1. 获取贷款产品列表

**接口地址:** `POST /api/load/product/list`

**请求参数:** 无

**响应示例:**
```json
{
  "code": 200,
  "message": "获取贷款产品列表成功",
  "data": [
    {
      "id": 1,
      "amountMin": 100.00,
      "amountMax": 1000.00,
      "cycleType": 7,
      "repayType": 0,
      "repayTypeText": "到期一次还本息",
      "odds": 1.5,
      "repayOrg": "平台自营",
      "remark": "7天短期贷款产品"
    }
  ]
}
```

### 2. 计算贷款利息

**接口地址:** `POST /api/load/product/calculateInterest`

**请求参数:**
```json
{
  "product_id": 1,
  "amount": 1000
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "利息计算成功",
  "data": {
    "amount": 1000,
    "interest": 105.00,
    "totalRepayment": 1105.00,
    "cycleType": 7,
    "odds": 1.5
  }
}
```

### 3. 提交贷款申请

**接口地址:** `POST /api/load/order/submit`

**请求参数:**
```json
{
  "product_id": 1,
  "amount": 1000,
  "card_url": "身份证正面图片URL",
  "card_back_url": "身份证反面图片URL",
  "capital_url": "手持身份证图片URL"
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "贷款申请提交成功",
  "data": {
    "orderId": 123,
    "orderNo": "Z1640995200001",
    "amount": 1000,
    "interest": 105.00,
    "totalRepayment": 1105.00
  }
}
```

**错误响应:**
```json
{
  "code": 4001,
  "message": "您还有未结清的贷款，无法申请新的贷款",
  "data": null
}
```

### 4. 获取用户贷款订单列表

**接口地址:** `POST /api/load/order/list`

**请求参数:**
```json
{
  "status": 0  // 可选，筛选状态：0待审核 1通过 2拒绝 3结清 4逾期
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "获取贷款订单列表成功",
  "data": {
    "data": [
      {
        "id": 123,
        "orderNo": "Z1640995200001",
        "amount": 1000.00,
        "disburseAmount": 1000.00,
        "interest": 105.00,
        "overdueInterest": 0.00,
        "totalRepayment": 1105.00,
        "status": 1,
        "statusText": "审核通过",
        "cycleType": 7,
        "rate": 1.5,
        "finalRepayTime": "2024-01-08 12:00:00",
        "disburseTime": "2024-01-01 12:00:00",
        "returnTime": null,
        "overduedays": 0,
        "refuseReason": null,
        "createdAt": "2024-01-01 10:00:00",
        "productInfo": {
          "repayOrg": "平台自营",
          "repayTypeText": "到期一次还本息"
        }
      }
    ],
    "pagination": {
      "current_page": 1,
      "last_page": 1,
      "per_page": 20,
      "total": 1
    }
  }
}
```

### 5. 获取贷款订单详情

**接口地址:** `POST /api/load/order/detail`

**请求参数:**
```json
{
  "order_id": 123
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "获取订单详情成功",
  "data": {
    "id": 123,
    "orderNo": "Z1640995200001",
    "amount": 1000.00,
    "disburseAmount": 1000.00,
    "interest": 105.00,
    "overdueInterest": 0.00,
    "totalRepayment": 1105.00,
    "status": 1,
    "statusText": "审核通过",
    "cycleType": 7,
    "rate": 1.5,
    "finalRepayTime": "2024-01-08 12:00:00",
    "disburseTime": "2024-01-01 12:00:00",
    "returnTime": null,
    "overduedays": 0,
    "refuseReason": null,
    "createdAt": "2024-01-01 10:00:00",
    "cardUrl": "身份证正面图片URL",
    "cardBackUrl": "身份证反面图片URL",
    "capitalUrl": "手持身份证图片URL",
    "productInfo": {
      "repayOrg": "平台自营",
      "repayTypeText": "到期一次还本息"
    }
  }
}
```

## 🏢 后台管理API

### 1. 贷款产品管理

- `GET /admin/load-products` - 产品列表页面
- `GET /admin/load-products/create` - 创建产品页面
- `POST /admin/load-products` - 保存新产品
- `GET /admin/load-products/{id}/edit` - 编辑产品页面
- `PUT /admin/load-products/{id}` - 更新产品
- `DELETE /admin/load-products/{id}` - 删除产品

### 2. 贷款订单管理

- `GET /admin/load-orders` - 订单列表页面
- `GET /admin/load-orders/{id}` - 订单详情页面
- `GET /admin/load-orders/{id}/approve` - 审核通过页面
- `POST /admin/load-orders/{id}/approve` - 执行审核通过
- `GET /admin/load-orders/{id}/reject` - 审核拒绝页面
- `POST /admin/load-orders/{id}/reject` - 执行审核拒绝
- `GET /admin/load-orders/{id}/repay` - 还款操作页面
- `POST /admin/load-orders/{id}/repay` - 执行还款操作

## 🔢 状态码说明

### 订单状态
- `0` - 待审核
- `1` - 审核通过
- `2` - 审核拒绝
- `3` - 已结清
- `4` - 已逾期

### 产品状态
- `0` - 未开启
- `1` - 已开启

### 冻结状态
- `1` - 正常
- `2` - 冻结

## 💰 业务逻辑说明

### 1. 利息计算公式
```
利息 = 贷款金额 × 日利率(%) × 贷款周期(天) ÷ 100
```

### 2. 逾期利息计算公式
```
逾期利息 = 审批金额 × 逾期天数 × 逾期利率(2.5%)
```

### 3. 业务规则
- 用户同时只能有一笔未结清贷款
- 审核通过后用户状态被冻结
- 所有贷款结清后用户状态解除冻结
- 超过还款日期自动标记为逾期
- 逾期订单每天产生逾期利息

## 🧪 测试用例

### 1. 正常申请流程
1. 获取产品列表 ✓
2. 选择产品计算利息 ✓
3. 提交申请 ✓
4. 后台审核通过 ✓
5. 用户余额增加 ✓
6. 用户状态冻结 ✓

### 2. 异常情况测试
1. 重复申请 → 应返回错误
2. 金额超出范围 → 应返回错误
3. 产品不可用 → 应返回错误
4. 用户已冻结 → 应返回错误

### 3. 逾期处理测试
1. 订单逾期自动检测 ✓
2. 逾期利息计算 ✓
3. 状态自动更新 ✓

## 📈 监控建议

建议监控以下指标：
- API响应时间
- 贷款申请成功率
- 审核通过率
- 逾期率
- 系统错误率

## 🔧 维护命令

```bash
# 手动更新逾期订单
php artisan loan:update-overdue

# 查看贷款统计
php artisan tinker
>>> $service = new App\Services\LoadService();
>>> $stats = $service->getStatistics();
>>> print_r($stats);
```
