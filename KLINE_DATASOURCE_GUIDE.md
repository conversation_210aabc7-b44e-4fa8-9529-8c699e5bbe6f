# K线数据源切换功能使用指南

本功能允许您在火币(<PERSON><PERSON><PERSON>)和币安(Binance)数据源之间切换，获取K线数据。

## 功能特点

- ✅ 最小化代码改动
- ✅ 保持原有代码结构
- ✅ 统一的数据格式
- ✅ 简单的配置切换
- ✅ 兼容现有K线脚本
- ✅ 支持WebSocket实时数据
- ✅ 自动数据源切换

## 配置方法

### 1. 环境变量配置

在 `.env` 文件中添加或修改：

```bash
# K线数据源配置 (huobi/binance)
KLINE_DATA_SOURCE=huobi
```

可选值：
- `huobi` - 使用火币数据源（默认）
- `binance` - 使用币安数据源

### 2. 切换数据源

**切换到币安：**
```bash
# 修改.env文件
KLINE_DATA_SOURCE=binance
```

**切换到火币：**
```bash
# 修改.env文件
KLINE_DATA_SOURCE=huobi
```

### 3. 重启K线进程

修改配置后，需要重启所有K线进程：

```bash
# 停止现有进程
pkill -f kline

# 重新启动K线进程
php public/exchange/kline_1min.php start -d
php public/exchange/kline_5min.php start -d
php public/exchange/kline_15min.php start -d
php public/exchange/kline_1day.php start -d
# ... 其他周期
```

## 测试功能

运行测试脚本验证功能：

```bash
php test_kline_datasource.php
```

测试脚本会：
- 检查当前配置的数据源
- 测试数据获取功能
- 对比两个数据源的数据格式
- 显示示例数据

## 技术实现

### 1. 新增Helper函数

在 `app/Common/functions.php` 中添加了以下函数：

**REST API函数：**
- `getKlineData($symbol, $period, $size)` - 根据配置获取K线数据
- `getHuobiKlineData($symbol, $period, $size)` - 获取火币数据
- `getBinanceKlineData($symbol, $period, $size)` - 获取币安数据
- `formatBinanceKlineData($data)` - 格式化币安数据

**WebSocket函数：**
- `getWebSocketUrl()` - 获取WebSocket连接URL
- `createWebSocketSubscriptions($symbols, $period)` - 创建订阅消息
- `processWebSocketMessage($data)` - 处理WebSocket消息
- `processHuobiWebSocketMessage($data)` - 处理火币WebSocket消息
- `processBinanceWebSocketMessage($data)` - 处理币安WebSocket消息

### 2. 修改的文件

**配置文件：**
- `.env` - 添加数据源配置
- `config/coin.php` - 添加配置读取

**K线脚本：**
- `public/exchange/kline_1min.php`
- `public/exchange/kline_5min.php`
- `public/exchange/kline_15min.php`
- `public/exchange/kline_1day.php`

### 3. 数据格式

两个数据源的数据都转换为统一格式：

```php
[
    'id' => 1640995200,        // 时间戳(秒)
    'open' => 47000.00,        // 开盘价
    'high' => 47500.00,        // 最高价
    'low' => 46800.00,         // 最低价
    'close' => 47200.00,       // 收盘价
    'amount' => 1234567.89,    // 成交额
    'vol' => 26.12,            // 成交量
    'count' => 1250            // 成交笔数
]
```

## 注意事项

### 1. WebSocket连接

✅ **已完全支持币安WebSocket**：
- 火币WebSocket：`wss://api.huobi.pro/ws`
- 币安WebSocket：`wss://stream.binance.com:9443/ws`
- 根据配置自动选择连接地址
- 自动处理不同的消息格式

### 2. API限制

- 火币API：无特殊限制
- 币安API：有请求频率限制，请避免过于频繁的请求

### 3. 网络连接

确保服务器能够访问：
- 火币API：`https://api.huobi.pro`
- 币安API：`https://api.binance.com`

### 4. 错误处理

如果数据获取失败，会：
- 记录错误日志
- 返回空数组
- 在控制台显示错误信息

## 故障排除

### 1. 数据获取失败

```bash
# 检查网络连接
curl -I https://api.huobi.pro/v1/common/timestamp
curl -I https://api.binance.com/api/v3/ping

# 运行测试脚本
php test_kline_datasource.php
```

### 2. 配置不生效

- 确认.env文件修改正确
- 重启所有K线进程
- 检查config/coin.php配置

### 3. 数据格式问题

- 查看测试脚本输出
- 检查日志文件 `storage/logs/laravel.log`
- 验证helper函数是否正确加载

## 扩展开发

如需添加其他数据源：

1. 在 `app/Common/functions.php` 中添加新的获取函数
2. 修改 `getKlineData()` 函数添加新的数据源判断
3. 更新配置文件支持新的数据源选项

## 总结

此实现方案：
- 最小化了代码改动
- 保持了原有架构
- 提供了简单的切换机制
- 确保了数据格式兼容性

通过简单修改配置文件即可在两个数据源之间切换，无需修改业务逻辑代码。
