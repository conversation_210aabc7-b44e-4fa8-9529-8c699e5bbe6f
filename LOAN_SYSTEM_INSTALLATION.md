# 贷款功能安装配置指南

## 📋 功能概述

本贷款功能完全按照Java版本实现，包含：
- ✅ 贷款产品管理（CRUD）
- ✅ 贷款订单管理（申请、审核、还款）
- ✅ 自动逾期检测和利息计算
- ✅ 资金流转和钱包记录
- ✅ 完整的后台管理界面

## 🚀 安装步骤

### 1. 数据库迁移

```bash
# 运行数据库迁移
php artisan migrate

# 如果需要回滚
php artisan migrate:rollback --step=2
```

### 2. 初始化贷款产品数据

```bash
# 运行种子文件
php artisan db:seed --class=LoadProductSeeder
```

### 3. 配置定时任务

在服务器的crontab中添加：
```bash
# 编辑crontab
crontab -e

# 添加以下行（每小时检查逾期订单）
0 * * * * cd /path/to/your/project && php artisan loan:update-overdue >> /dev/null 2>&1
```

或者手动运行：
```bash
php artisan loan:update-overdue
```

### 4. 配置后台菜单

执行SQL文件添加后台菜单：
```bash
mysql -u username -p database_name < database/sql/loan_menu.sql
```

或者在后台管理系统中手动添加菜单：
- 贷款管理 (父菜单)
  - 贷款产品 (/load-products)
  - 贷款订单 (/load-orders)

## 📡 API接口文档

### 用户端API

**基础URL:** `/api/`

#### 1. 获取贷款产品列表
```
POST /api/load/product/list
Headers: Authorization: Bearer {token}
Response: {
  "code": 200,
  "data": [
    {
      "id": 1,
      "amountMin": 100.00,
      "amountMax": 1000.00,
      "cycleType": 7,
      "odds": 1.5,
      "repayOrg": "平台自营"
    }
  ]
}
```

#### 2. 计算贷款利息
```
POST /api/load/product/calculateInterest
Headers: Authorization: Bearer {token}
Body: {
  "product_id": 1,
  "amount": 1000
}
Response: {
  "code": 200,
  "data": {
    "amount": 1000,
    "interest": 105.00,
    "totalRepayment": 1105.00
  }
}
```

#### 3. 提交贷款申请
```
POST /api/load/order/submit
Headers: Authorization: Bearer {token}
Body: {
  "product_id": 1,
  "amount": 1000,
  "card_url": "身份证正面图片URL",
  "card_back_url": "身份证反面图片URL", 
  "capital_url": "手持身份证图片URL"
}
```

#### 4. 获取用户贷款订单列表
```
POST /api/load/order/list
Headers: Authorization: Bearer {token}
Body: {
  "status": 0  // 可选，筛选状态
}
```

#### 5. 获取贷款订单详情
```
POST /api/load/order/detail
Headers: Authorization: Bearer {token}
Body: {
  "order_id": 1
}
```

### 后台管理API

**基础URL:** `/admin/`

#### 1. 贷款产品管理
- `GET /admin/load-products` - 产品列表
- `GET /admin/load-products/create` - 创建产品页面
- `POST /admin/load-products` - 保存产品
- `GET /admin/load-products/{id}/edit` - 编辑产品页面
- `PUT /admin/load-products/{id}` - 更新产品
- `DELETE /admin/load-products/{id}` - 删除产品

#### 2. 贷款订单管理
- `GET /admin/load-orders` - 订单列表
- `GET /admin/load-orders/{id}` - 订单详情
- `GET /admin/load-orders/{id}/approve` - 审核通过页面
- `GET /admin/load-orders/{id}/reject` - 审核拒绝页面
- `GET /admin/load-orders/{id}/repay` - 还款操作页面

## 🔧 配置说明

### 1. 订单状态说明
- 0: 待审核
- 1: 审核通过
- 2: 审核拒绝
- 3: 已结清
- 4: 已逾期

### 2. 利息计算公式
```
利息 = 贷款金额 × 日利率(%) × 贷款周期(天) ÷ 100
逾期利息 = 审批金额 × 逾期天数 × 逾期利率(2.5%)
```

### 3. 业务流程
1. **申请:** 用户选择产品 → 填写金额 → 上传证件 → 提交申请
2. **审核:** 管理员审核 → 通过则放款 → 拒绝则更新状态
3. **逾期:** 系统自动检测 → 计算逾期利息 → 更新状态
4. **还款:** 管理员操作 → 更新状态 → 解除冻结

## 🧪 测试

运行测试脚本：
```bash
php test_loan_system.php
```

## 📁 文件结构

```
app/
├── Models/
│   ├── LoadProduct.php          # 贷款产品模型
│   └── LoadOrder.php            # 贷款订单模型
├── Http/Controllers/
│   ├── Api/V1/
│   │   ├── LoadProductController.php  # 用户端产品API
│   │   └── LoadOrderController.php    # 用户端订单API
│   └── Admin/Controllers/
│       ├── LoadProductController.php  # 后台产品管理
│       └── LoadOrderController.php    # 后台订单管理
├── Http/Requests/
│   └── LoadOrderRequest.php     # 订单请求验证
├── Services/
│   └── LoadService.php          # 贷款业务服务
└── Console/Commands/
    └── UpdateOverdueLoans.php   # 逾期更新命令

database/
├── migrations/
│   ├── 2024_01_01_000001_create_load_product_table.php
│   └── 2024_01_01_000002_create_load_order_table.php
├── seeds/
│   └── LoadProductSeeder.php    # 产品数据初始化
└── sql/
    └── loan_menu.sql           # 后台菜单配置

routes/
└── yx_api.php                  # API路由配置
```

## ⚠️ 注意事项

1. **依赖检查:** 确保项目中存在以下模型类：
   - `Users` (用户模型)
   - `UserWallet` (用户钱包模型)
   - `WalletLog` (钱包日志模型)

2. **权限配置:** 根据实际的权限管理系统调整权限配置

3. **图片上传:** 确保图片上传功能正常工作

4. **邮件/短信通知:** 可以根据需要添加审核结果通知功能

5. **数据备份:** 在生产环境部署前请备份数据库

## 🔄 后续扩展

可以考虑添加的功能：
- 贷款申请邮件/短信通知
- 逾期提醒功能
- 贷款统计报表
- 风控规则配置
- 分期还款功能
